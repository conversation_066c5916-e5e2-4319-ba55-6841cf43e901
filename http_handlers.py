import asyncio
import logging
import time
import os
import wave
from datetime import datetime
from aiohttp import web

import config
from webrtc_client import start_streaming_session
from webrtc_twoway_client import start_twoway_streaming_session
from onvif_templates import PULL_MESSAGES_NOTIFICATION_TEMPLATE

# Audio accumulation system for better debugging
class AudioAccumulator:
    def __init__(self):
        self.audio_buffer = bytearray()
        self.start_time = None
        self.packet_count = 0
        self.last_save_time = None
        self.save_interval = 5.0  # Save every 5 seconds
        self.max_buffer_size = 8000 * 10  # 10 seconds at 8kHz * 1 byte per sample

    async def add_audio_data(self, audio_data, audio_format, timestamp):
        """Add audio data to buffer and save when appropriate"""
        current_time = time.time()

        if self.start_time is None:
            self.start_time = current_time
            self.last_save_time = current_time
            logging.info(f"🎤 Started new audio accumulation session")

        # Add data to buffer
        self.audio_buffer.extend(audio_data)
        self.packet_count += 1

        # Check if we should save (time-based or size-based)
        time_to_save = (current_time - self.last_save_time) >= self.save_interval
        size_to_save = len(self.audio_buffer) >= self.max_buffer_size

        if time_to_save or size_to_save:
            await self._save_accumulated_audio(audio_format, timestamp)

    async def _save_accumulated_audio(self, audio_format, timestamp):
        """Save accumulated audio data to file"""
        if len(self.audio_buffer) == 0:
            return

        try:
            # Create audio_logs directory if it doesn't exist
            os.makedirs('audio_logs', exist_ok=True)

            # Clean up old audio files (keep last N files)
            try:
                existing_files = sorted([f for f in os.listdir('audio_logs') if f.startswith('audio_accumulated_')])
                if len(existing_files) >= config.onvif_audio_file_rotation_count:
                    files_to_remove = existing_files[:-config.onvif_audio_file_rotation_count + 1]
                    for old_file in files_to_remove:
                        os.unlink(os.path.join('audio_logs', old_file))
                        logging.debug(f"[AUDIO ACCUMULATOR] Removed old audio file: {old_file}")
            except Exception as e:
                logging.warning(f"[AUDIO ACCUMULATOR] Error cleaning up old audio files: {e}")

            # Calculate duration based on actual format
            if audio_format == 'g711':
                # G.711: 1 byte = 1 sample at 8kHz
                duration = len(self.audio_buffer) / 8000.0
            else:
                # PCM: 2 bytes = 1 sample at 8kHz
                duration = len(self.audio_buffer) / (8000.0 * 2)

            filename_base = f"audio_logs/audio_accumulated_{timestamp}_{audio_format}_{self.packet_count}pkts_{duration:.1f}s"

            # Save raw data
            raw_filename = f"{filename_base}.raw"
            with open(raw_filename, 'wb') as f:
                f.write(self.audio_buffer)

            # Save as WAV for easier playback
            wav_filename = f"{filename_base}.wav"
            try:
                # Always convert to 16-bit PCM for WAV compatibility
                if audio_format == 'g711':
                    # Convert G.711 A-law to 16-bit PCM
                    pcm_data = self._convert_g711_to_pcm(self.audio_buffer)
                    logging.info(f"[AUDIO ACCUMULATOR] Converting {len(self.audio_buffer)} G.711 bytes to {len(pcm_data)} PCM bytes")
                else:
                    # Data is already PCM
                    pcm_data = bytes(self.audio_buffer)
                    logging.info(f"[AUDIO ACCUMULATOR] Using {len(pcm_data)} bytes as PCM data")

                with wave.open(wav_filename, 'wb') as wav_file:
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit PCM
                    wav_file.setframerate(8000)  # 8kHz
                    wav_file.writeframes(pcm_data)

                logging.info(f"🎤 Saved accumulated audio: {len(self.audio_buffer)} bytes, "
                           f"{self.packet_count} packets, {duration:.1f}s duration")
                logging.info(f"🎤 Files: {raw_filename}, {wav_filename}")

            except Exception as e:
                logging.warning(f"[AUDIO ACCUMULATOR] Error creating WAV file: {e}")
                logging.info(f"🎤 Saved raw audio: {raw_filename}")

            # Reset buffer
            self.audio_buffer.clear()
            self.packet_count = 0
            self.last_save_time = time.time()

        except Exception as e:
            logging.error(f"[AUDIO ACCUMULATOR] Error saving accumulated audio: {e}")

    def _convert_g711_to_pcm(self, g711_data):
        """Convert G.711 A-law data to 16-bit PCM"""
        import numpy as np

        # G.711 A-law to PCM conversion lookup table (simplified)
        alaw_to_pcm = np.array([
            -5504, -5248, -6016, -5760, -4480, -4224, -4992, -4736,
            -7552, -7296, -8064, -7808, -6528, -6272, -7040, -6784,
            -2752, -2624, -3008, -2880, -2240, -2112, -2496, -2368,
            -3776, -3648, -4032, -3904, -3264, -3136, -3520, -3392,
            -22016, -20992, -24064, -23040, -17920, -16896, -19968, -18944,
            -30208, -29184, -32256, -31232, -26112, -25088, -28160, -27136,
            -11008, -10496, -12032, -11520, -8960, -8448, -9984, -9472,
            -15104, -14592, -16128, -15616, -13056, -12544, -14080, -13568,
            -344, -328, -376, -360, -280, -264, -312, -296,
            -472, -456, -504, -488, -408, -392, -440, -424,
            -88, -72, -120, -104, -24, -8, -56, -40,
            -216, -200, -248, -232, -152, -136, -184, -168,
            -1376, -1312, -1504, -1440, -1120, -1056, -1248, -1184,
            -1888, -1824, -2016, -1952, -1632, -1568, -1760, -1696,
            -688, -656, -752, -720, -560, -528, -624, -592,
            -944, -912, -1008, -976, -816, -784, -880, -848,
            5504, 5248, 6016, 5760, 4480, 4224, 4992, 4736,
            7552, 7296, 8064, 7808, 6528, 6272, 7040, 6784,
            2752, 2624, 3008, 2880, 2240, 2112, 2496, 2368,
            3776, 3648, 4032, 3904, 3264, 3136, 3520, 3392,
            22016, 20992, 24064, 23040, 17920, 16896, 19968, 18944,
            30208, 29184, 32256, 31232, 26112, 25088, 28160, 27136,
            11008, 10496, 12032, 11520, 8960, 8448, 9984, 9472,
            15104, 14592, 16128, 15616, 13056, 12544, 14080, 13568,
            344, 328, 376, 360, 280, 264, 312, 296,
            472, 456, 504, 488, 408, 392, 440, 424,
            88, 72, 120, 104, 24, 8, 56, 40,
            216, 200, 248, 232, 152, 136, 184, 168,
            1376, 1312, 1504, 1440, 1120, 1056, 1248, 1184,
            1888, 1824, 2016, 1952, 1632, 1568, 1760, 1696,
            688, 656, 752, 720, 560, 528, 624, 592,
            944, 912, 1008, 976, 816, 784, 880, 848
        ], dtype=np.int16)

        try:
            # Convert each A-law byte to PCM
            alaw_bytes = np.frombuffer(g711_data, dtype=np.uint8)
            pcm_samples = alaw_to_pcm[alaw_bytes]
            return pcm_samples.tobytes()
        except Exception as e:
            logging.warning(f"[AUDIO ACCUMULATOR] Error converting G.711 to PCM: {e}")
            # Return original data as fallback
            return g711_data

# Global audio accumulator instance
audio_accumulator = AudioAccumulator()

async def accumulate_and_save_audio(audio_data, audio_format, timestamp):
    """Public interface for audio accumulation"""
    await audio_accumulator.add_audio_data(audio_data, audio_format, timestamp)

async def finalize_audio_session():
    """Force save any remaining audio data"""
    if len(audio_accumulator.audio_buffer) > 0:
        timestamp = int(time.time() * 1000)
        await audio_accumulator._save_accumulated_audio("pcm", timestamp)
        logging.info("🎤 Finalized audio session - saved remaining buffer")

def _detect_audio_format(audio_data, data_size):
    """Detect if audio data is G.711 or PCM based on characteristics"""
    try:
        # For ONVIF backchannel, assume G.711 unless clearly PCM
        # The Go proxy always sends "audio/pcm" but it's usually G.711 data

        if data_size == 160:
            # Classic G.711 packet size (20ms at 8kHz)
            logging.info(f"[AUDIO DETECTION] Size {data_size} is classic G.711 packet (20ms)")
            return 'g711'
        elif data_size < 500:
            # Small packets are typically G.711
            logging.info(f"[AUDIO DETECTION] Small size {data_size} suggests G.711")
            return 'g711'
        elif data_size % 160 == 0:
            # Multiple of 160 suggests G.711 packets
            packets = data_size // 160
            logging.info(f"[AUDIO DETECTION] Size {data_size} = {packets} G.711 packets")
            return 'g711'
        else:
            # Check if data looks like G.711 by examining byte distribution
            if data_size >= 10:
                byte_values = list(audio_data[:10])
                # G.711 has more varied byte distribution than typical PCM
                unique_values = len(set(byte_values))
                has_high_bytes = any(b > 200 for b in byte_values)

                if unique_values >= 7 or has_high_bytes:
                    logging.info(f"[AUDIO DETECTION] Byte distribution suggests G.711 (unique={unique_values}, high_bytes={has_high_bytes})")
                    return 'g711'
                else:
                    logging.info(f"[AUDIO DETECTION] Limited byte distribution suggests PCM")
                    return 'pcm'
            else:
                # Default to G.711 for ONVIF
                logging.info(f"[AUDIO DETECTION] Defaulting to G.711 for ONVIF backchannel")
                return 'g711'

    except Exception as e:
        logging.warning(f"[AUDIO DETECTION] Error detecting format: {e}")
        return 'g711'  # Default to G.711 for ONVIF


async def handle_start(request):
    if config.current_stream_task and not config.current_stream_task.done():
        return web.Response(text="A streaming session is already in progress.", status=409)
    try:
        data = await request.json()
        config.ROOM_ID, config.FERMAX_OAUTH_TOKEN, config.APP_TOKEN = data['ROOM_ID'], data['FERMAX_OAUTH_TOKEN'], data['APP_TOKEN']
        # Server URL parameter (optional, defaults to calldivertsignserver01)
        config.SERVER_URL = data.get('SERVER_URL', 'https://calldivertsignserver01-pro-west-europe-blue.fermax.io:443')
    except Exception:
        return web.Response(text="Invalid JSON payload.", status=400)
    def clear_task(task):
        config.current_stream_task = None
    config.current_stream_task = asyncio.create_task(start_streaming_session())
    config.current_stream_task.add_done_callback(clear_task)
    return web.Response(text="Doorbell session initiated.", status=202)


async def handle_start_twoway(request):
    if config.current_stream_task and not config.current_stream_task.done():
        return web.Response(text="A streaming session is already in progress.", status=409)
    try:
        data = await request.json()
        config.ROOM_ID, config.FERMAX_OAUTH_TOKEN, config.APP_TOKEN = data['ROOM_ID'], data['FERMAX_OAUTH_TOKEN'], data['APP_TOKEN']
        # Server URL parameter (optional, defaults to calldivertsignserver01)
        config.SERVER_URL = data.get('SERVER_URL', 'https://calldivertsignserver01-pro-west-europe-blue.fermax.io:443')
    except Exception:
        return web.Response(text="Invalid JSON payload.", status=400)
    def clear_task(task):
        config.current_stream_task = None
    config.current_stream_task = asyncio.create_task(start_twoway_streaming_session())
    config.current_stream_task.add_done_callback(clear_task)
    return web.Response(text="Two-way audio doorbell session initiated.", status=202)


async def handle_stop(request):
    if not config.current_stream_task or config.current_stream_task.done():
        return web.Response(text="No active session to stop.", status=404)
    
    # Cancel the WebRTC streaming task
    config.current_stream_task.cancel()
    
    # Wait a moment for cleanup to complete
    try:
        await asyncio.wait_for(config.current_stream_task, timeout=2.0)
    except (asyncio.CancelledError, asyncio.TimeoutError):
        pass  # Expected when task is cancelled
    
    # Finalize any remaining audio data
    await finalize_audio_session()

    # FFmpeg continues running - stream generators will switch back to dummy content
    logging.info("Stop endpoint: WebRTC session cancelled - stream will revert to dummy content")

    return web.Response(text="Stop command issued.", status=200)


async def handle_doorbell_press_with_image(request):
    """
    Starts a WebRTC connection, captures the first frame image, triggers doorbell press,
    and returns the captured image as a notification.
    """
    if config.current_stream_task and not config.current_stream_task.done():
        return web.Response(text="A streaming session is already in progress.", status=409)
    
    try:
        # Parse request parameters (same as /start)
        data = await request.json()
        config.ROOM_ID = data['ROOM_ID']
        config.FERMAX_OAUTH_TOKEN = data['FERMAX_OAUTH_TOKEN'] 
        config.APP_TOKEN = data['APP_TOKEN']
        config.SERVER_URL = data.get('SERVER_URL', 'https://calldivertsignserver01-pro-west-europe-blue.fermax.io:443')
    except Exception:
        return web.Response(text="Invalid JSON payload.", status=400)
    
    # Clear the frame saved flag before starting
    config.frame_saved.clear()
    
    # Start WebRTC connection to capture image
    from webrtc_client import start_streaming_session
    
    def clear_task(task):
        config.current_stream_task = None
    
    config.current_stream_task = asyncio.create_task(start_streaming_session())
    config.current_stream_task.add_done_callback(clear_task)
    
    # Wait for first frame to be actually saved (up to 15 seconds for faster response)
    try:
        await asyncio.wait_for(config.frame_saved.wait(), timeout=15.0)
        logging.info("First frame successfully saved to last_call.png")
    except asyncio.TimeoutError:
        # Cancel the connection if no frame was captured
        if config.current_stream_task and not config.current_stream_task.done():
            config.current_stream_task.cancel()
        return web.Response(text="Failed to capture doorbell image - timeout after 15s", status=500)
    
    # CRITICAL: Set the doorbell snapshot BEFORE sending ONVIF notification
    import time
    config.doorbell_snapshot_until = time.time() + 5.0
    logging.info("Doorbell snapshot set for 5 seconds in ONVIF /onvif/snapshot endpoint")
    
    # Trigger doorbell press event
    logging.info("Doorbell ON event triggered.")
    press_time = datetime.utcnow()
    event_press_msg = PULL_MESSAGES_NOTIFICATION_TEMPLATE.format(
        utc_time=press_time.isoformat() + "Z",
        state="true"
    )
    await config.g_event_queue.put(event_press_msg)
    config.g_event_available.set()
    
    # Wait for the desired duration
    await asyncio.sleep(3)
    
    # Trigger doorbell release event
    logging.info("Doorbell OFF event triggered.")
    release_time = datetime.utcnow()
    event_release_msg = PULL_MESSAGES_NOTIFICATION_TEMPLATE.format(
        utc_time=release_time.isoformat() + "Z",
        state="false"
    )
    await config.g_event_queue.put(event_release_msg)
    config.g_event_available.set()
    
    # Clean up connection
    if config.current_stream_task and not config.current_stream_task.done():
        config.current_stream_task.cancel()
        try:
            await asyncio.wait_for(config.current_stream_task, timeout=2.0)
        except (asyncio.CancelledError, asyncio.TimeoutError):
            pass
    
    return web.Response(text="Doorbell press completed. Snapshot changed for 5 seconds.", status=200)


async def handle_update_snapshot(request):
    """
    Starts a WebRTC connection, captures the first frame, and saves it as outside_still.jpg
    to replace the current dummy image. No doorbell press events are triggered.
    """
    if config.current_stream_task and not config.current_stream_task.done():
        return web.Response(text="A streaming session is already in progress.", status=409)
    
    try:
        # Parse request parameters (same as /start)
        data = await request.json()
        config.ROOM_ID = data['ROOM_ID']
        config.FERMAX_OAUTH_TOKEN = data['FERMAX_OAUTH_TOKEN'] 
        config.APP_TOKEN = data['APP_TOKEN']
        config.SERVER_URL = data.get('SERVER_URL', 'https://calldivertsignserver01-pro-west-europe-blue.fermax.io:443')
    except Exception:
        return web.Response(text="Invalid JSON payload.", status=400)
    
    # Set custom save path and clear the snapshot saved flag
    config.custom_save_path = config.STILL_IMAGE_PATH  # Save to outside_still.jpg
    config.snapshot_saved.clear()
    
    # Start WebRTC connection to capture image
    from webrtc_client import start_streaming_session
    
    def clear_task(task):
        config.current_stream_task = None
        # Reset custom save path when task completes
        config.custom_save_path = None
    
    config.current_stream_task = asyncio.create_task(start_streaming_session())
    config.current_stream_task.add_done_callback(clear_task)
    
    # Wait for snapshot to be saved (up to 15 seconds)
    try:
        await asyncio.wait_for(config.snapshot_saved.wait(), timeout=15.0)
        logging.info(f"Snapshot successfully saved to {config.STILL_IMAGE_PATH}")
    except asyncio.TimeoutError:
        # Cancel the connection if no frame was captured
        if config.current_stream_task and not config.current_stream_task.done():
            config.current_stream_task.cancel()
        # Reset custom save path on timeout
        config.custom_save_path = None
        return web.Response(text="Failed to capture snapshot - timeout after 15s", status=500)
    
    # Clean up connection
    if config.current_stream_task and not config.current_stream_task.done():
        config.current_stream_task.cancel()
        try:
            await asyncio.wait_for(config.current_stream_task, timeout=2.0)
        except (asyncio.CancelledError, asyncio.TimeoutError):
            pass
    
    # Reset custom save path after successful completion
    config.custom_save_path = None
    
    return web.Response(text=f"Snapshot updated successfully. Saved to {config.STILL_IMAGE_PATH}.", status=200)


async def handle_audio_input(request):
    """
    Receives audio data from ONVIF clients for backchannel communication.
    Logs audio characteristics and saves to files for verification.
    """
    import os
    import time
    import wave
    import struct
    
    # Check Content-Type header to determine audio format
    content_type = request.headers.get('Content-Type', '').lower()
    content_length = request.headers.get('Content-Length', '0')
    
    if not content_type.startswith('audio/'):
        return web.Response(text="Invalid Content-Type. Expected audio/* format.", status=400)
    
    # Determine audio format from Content-Type and data characteristics
    audio_format = 'unknown'
    if 'pcm' in content_type:
        # Note: ONVIF backchannel often sends G.711 data with "audio/pcm" content-type
        # We'll detect the actual format based on data size and characteristics
        audio_format = 'pcm_or_g711'
    elif 'g711' in content_type or 'ulaw' in content_type or 'alaw' in content_type:
        audio_format = 'g711'
    elif 'wav' in content_type:
        audio_format = 'wav'
    
    try:
        # Read audio data
        audio_data = await request.read()
        data_size = len(audio_data)
        
        if data_size == 0:
            return web.Response(text="No audio data received.", status=400)
        
        if data_size > 1024 * 1024:  # 1MB limit
            return web.Response(text="Audio data too large (max 1MB).", status=413)
        
        # Log audio reception
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        logging.info(f"[AUDIO INPUT] Received {data_size} bytes of {audio_format} audio data")
        logging.info(f"[AUDIO INPUT] Content-Type: {content_type}, Content-Length: {content_length}")
        
        # Enhanced audio format analysis
        if audio_format == 'pcm_or_g711':
            # Detect if this is actually G.711 data or true PCM
            detected_format = _detect_audio_format(audio_data, data_size)
            audio_format = detected_format
            logging.info(f"[AUDIO INPUT] Format detection: {detected_format} (was labeled as PCM)")

            # Show first few bytes for debugging
            if data_size >= 8:
                first_bytes = list(audio_data[:8])
                logging.info(f"[AUDIO INPUT] First 8 bytes: {first_bytes} (for format verification)")
        elif audio_format == 'pcm':
            # Assume 16-bit PCM samples for analysis
            if data_size >= 2:
                sample_count = data_size // 2
                logging.info(f"[AUDIO INPUT] PCM analysis: ~{sample_count} samples (assuming 16-bit)")

                # Basic silence detection on first few samples
                if data_size >= 10:
                    first_samples = struct.unpack('<5h', audio_data[:10])
                    max_amplitude = max(abs(s) for s in first_samples)
                    logging.info(f"[AUDIO INPUT] Sample amplitude check: max={max_amplitude} (of 32767)")
        
        # Queue audio data if enabled
        if config.onvif_audio_logging_enabled:
            try:
                # Put audio data in queue for processing (non-blocking)
                config.onvif_audio_queue.put_nowait({
                    'data': audio_data,
                    'format': audio_format,
                    'content_type': content_type,
                    'timestamp': timestamp,
                    'size': data_size
                })
                logging.debug(f"[AUDIO INPUT] Queued audio data for processing")
            except asyncio.QueueFull:
                logging.warning(f"[AUDIO INPUT] Audio queue full, dropping audio data")
        
        # Save to file if enabled - use improved accumulation system
        if config.onvif_audio_save_to_file:
            await accumulate_and_save_audio(audio_data, audio_format, timestamp)
        
        # Audio statistics tracking
        total_received = getattr(config, 'onvif_audio_total_received', 0) + data_size
        setattr(config, 'onvif_audio_total_received', total_received)
        
        if total_received % (1024 * 10) == 0:  # Log every 10KB received
            logging.info(f"[AUDIO INPUT] Total audio received: {total_received} bytes")
        
        return web.Response(text=f"Audio received: {data_size} bytes ({audio_format})", status=200)
        
    except Exception as e:
        logging.error(f"[AUDIO INPUT] Error processing audio data: {e}", exc_info=True)
        return web.Response(text="Internal server error processing audio data.", status=500)
