# --- ONVIF XML Templates ---
PROBE_MATCH_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:a="http://www.w3.org/2005/08/addressing">
   <s:Header>
      <a:MessageID>urn:uuid:{message_id}</a:MessageID>
      <a:RelatesTo>{relates_to}</a:RelatesTo>
      <a:To s:mustUnderstand="1">http://www.w3.org/2005/08/addressing/anonymous</a:To>
      <a:Action s:mustUnderstand="1">http://schemas.xmlsoap.org/ws/2005/04/discovery/ProbeMatches</a:Action>
   </s:Header>
   <s:Body>
      <d:ProbeMatches xmlns:d="http://schemas.xmlsoap.org/ws/2005/04/discovery">
         <d:ProbeMatch>
            <a:EndpointReference>
               <a:Address>{device_uuid}</a:Address>
            </a:EndpointReference>
            <d:Types>dn:NetworkVideoTransmitter</d:Types>
            <d:Scopes>onvif://www.onvif.org/Profile/T onvif://www.onvif.org/name/FermaxDoorbell onvif://www.onvif.org/hardware/VirtualCamera</d:Scopes>
            <d:XAddrs>http://{server_ip}:{onvif_port}/onvif/device_service</d:XAddrs>
            <d:MetadataVersion>1</d:MetadataVersion>
         </d:ProbeMatch>
      </d:ProbeMatches>
   </s:Body>
</s:Envelope>
"""

GET_DEVICE_INFORMATION_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:tds="http://www.onvif.org/ver10/device/wsdl">
    <s:Body>
        <tds:GetDeviceInformationResponse>
            <tds:Manufacturer>FERMAX</tds:Manufacturer>
            <tds:Model>DuoxMe Doorbell</tds:Model>
            <tds:FirmwareVersion>1.0.0</tds:FirmwareVersion>
            <tds:SerialNumber>{serial_number}</tds:SerialNumber>
            <tds:HardwareId>001</tds:HardwareId>
        </tds:GetDeviceInformationResponse>
    </s:Body>
</s:Envelope>
"""

GET_SERVICES_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:tds="http://www.onvif.org/ver10/device/wsdl">
    <s:Body>
        <tds:GetServicesResponse>
            <tds:Service>
                <tds:Namespace>http://www.onvif.org/ver10/device/wsdl</tds:Namespace>
                <tds:XAddr>http://{server_ip}:{onvif_port}/onvif/device_service</tds:XAddr>
                <tds:Version>
                    <tds:Major>1</tds:Major>
                    <tds:Minor>0</tds:Minor>
                </tds:Version>
            </tds:Service>
            <tds:Service>
                <tds:Namespace>http://www.onvif.org/ver10/media/wsdl</tds:Namespace>
                <tds:XAddr>http://{server_ip}:{onvif_port}/onvif/device_service</tds:XAddr>
                <tds:Version>
                    <tds:Major>1</tds:Major>
                    <tds:Minor>0</tds:Minor>
                </tds:Version>
            </tds:Service>
            <tds:Service>
                <tds:Namespace>http://www.onvif.org/ver10/events/wsdl</tds:Namespace>
                <tds:XAddr>http://{server_ip}:{onvif_port}/onvif/device_service</tds:XAddr>
                <tds:Version>
                    <tds:Major>1</tds:Major>
                    <tds:Minor>0</tds:Minor>
                </tds:Version>
            </tds:Service>
        </tds:GetServicesResponse>
    </s:Body>
</s:Envelope>
"""

GET_SERVICE_CAPABILITIES_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:tns="http://www.onvif.org/ver10/media/wsdl">
    <s:Body>
        <tns:GetServiceCapabilitiesResponse>
            <tns:Capabilities ProfileCapabilities_MaximumNumberOfProfiles="1" StreamingCapabilities_RTP_Multicast="false" StreamingCapabilities_RTP_TCP="true" StreamingCapabilities_RTP_RTSP_TCP="true"/>
        </tns:GetServiceCapabilitiesResponse>
    </s:Body>
</s:Envelope>
"""

GET_CAPABILITIES_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:tds="http://www.onvif.org/ver10/device/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <tds:GetCapabilitiesResponse>
            <tds:Capabilities>
                <tt:Media>
                    <tt:XAddr>http://{server_ip}:{onvif_port}/onvif/device_service</tt:XAddr>
                    <tt:StreamingCapabilities>
                        <tt:RTP_Multicast>false</tt:RTP_Multicast>
                        <tt:RTP_TCP>true</tt:RTP_TCP>
                        <tt:RTP_RTSP_TCP>true</tt:RTP_RTSP_TCP>
                        <tt:TwoWayAudio>true</tt:TwoWayAudio>
                    </tt:StreamingCapabilities>
                </tt:Media>
                <tt:Events>
                    <tt:XAddr>http://{server_ip}:{onvif_port}/onvif/device_service</tt:XAddr>
                    <tt:WSPullPointSupport>true</tt:WSPullPointSupport>
                </tt:Events>
            </tds:Capabilities>
        </tds:GetCapabilitiesResponse>
    </s:Body>
</s:Envelope>
"""

GET_EVENT_PROPERTIES_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:tns1="http://www.onvif.org/ver10/topics" xmlns:wsnt="http://docs.oasis-open.org/wsn/b-2" xmlns:tev="http://www.onvif.org/ver10/events/wsdl">
    <s:Body>
        <tev:GetEventPropertiesResponse>
            <wsnt:TopicNamespaceLocation>http://www.onvif.org/ver10/topics/topicns.xml</wsnt:TopicNamespaceLocation>
            <wsnt:FixedTopicSet>true</wsnt:FixedTopicSet>
            <wsnt:TopicSet>
                <tns1:Device>
                    <tns1:Trigger>
                        <tns1:DigitalInput/>
                    </tns1:Trigger>
                </tns1:Device>
            </wsnt:TopicSet>
            <wsnt:TopicExpressionDialect>http://www.onvif.org/ver10/tev/topicExpression/ConcreteSet</wsnt:TopicExpressionDialect>
            <wsnt:MessageContentFilterDialect>http://www.onvif.org/ver10/tev/messageContentFilter/ItemFilter</wsnt:MessageContentFilterDialect>
        </tev:GetEventPropertiesResponse>
    </s:Body>
</s:Envelope>
"""

CREATE_PULL_POINT_SUBSCRIPTION_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:wsa="http://www.w3.org/2005/08/addressing" xmlns:wsnt="http://docs.oasis-open.org/wsn/b-2">
    <s:Header>
        <wsa:Action s:mustUnderstand="1">http://docs.oasis-open.org/wsn/bw-2/NotificationProducer/CreatePullPointSubscriptionResponse</wsa:Action>
        <wsa:RelatesTo>{relates_to}</wsa:RelatesTo>
    </s:Header>
    <s:Body>
        <wsnt:CreatePullPointSubscriptionResponse>
            <wsnt:SubscriptionReference>
                <wsa:Address>http://{server_ip}:{onvif_port}/onvif/device_service</wsa:Address>
                <wsa:ReferenceParameters>
                    <SubscriptionId xmlns="http://www.scrypted.video/onvif">virtual-subscription-1</SubscriptionId>
                </wsa:ReferenceParameters>
            </wsnt:SubscriptionReference>
            <wsnt:CurrentTime>{current_time}</wsnt:CurrentTime>
            <wsnt:TerminationTime>{termination_time}</wsnt:TerminationTime>
        </wsnt:CreatePullPointSubscriptionResponse>
    </s:Body>
</s:Envelope>
"""

PULL_MESSAGES_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:wsnt="http://docs.oasis-open.org/wsn/b-2">
    <s:Body>
        <wsnt:PullMessagesResponse>
            <wsnt:CurrentTime>{current_time}</wsnt:CurrentTime>
            <wsnt:TerminationTime>{termination_time}</wsnt:TerminationTime>
        </wsnt:PullMessagesResponse>
    </s:Body>
</s:Envelope>
"""

PULL_MESSAGES_NOTIFICATION_TEMPLATE = """<wsnt:NotificationMessage>
    <wsnt:Topic Dialect="http://www.onvif.org/ver10/tev/topicExpression/ConcreteSet">
        tns1:Device/Trigger/DigitalInput
    </wsnt:Topic>
    <wsnt:Message>
        <tt:Message UtcTime="{utc_time}" PropertyOperation="Changed">
            <tt:Source>
                <tt:SimpleItem Name="Token" Value="doorbell_input"/>
            </tt:Source>
            <tt:Data>
                <tt:SimpleItem Name="State" Value="{state}"/>
            </tt:Data>
        </tt:Message>
    </wsnt:Message>
</wsnt:NotificationMessage>"""

PULL_MESSAGES_RESPONSE_WITH_NOTIFICATION_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:wsnt="http://docs.oasis-open.org/wsn/b-2" xmlns:tns1="http://www.onvif.org/ver10/topics" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <wsnt:PullMessagesResponse>
            <wsnt:CurrentTime>{current_time}</wsnt:CurrentTime>
            <wsnt:TerminationTime>{termination_time}</wsnt:TerminationTime>
            {notification_messages}
        </wsnt:PullMessagesResponse>
    </s:Body>
</s:Envelope>
"""

RENEW_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:wsnt="http://docs.oasis-open.org/wsn/b-2">
    <s:Body>
        <wsnt:RenewResponse>
            <wsnt:TerminationTime>{termination_time}</wsnt:TerminationTime>
            <wsnt:CurrentTime>{current_time}</wsnt:CurrentTime>
        </wsnt:RenewResponse>
    </s:Body>
</s:Envelope>
"""

GET_SNAPSHOT_URI_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:trt="http://www.onvif.org/ver10/media/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <trt:GetSnapshotUriResponse>
            <trt:MediaUri>
                <tt:Uri>http://{server_ip}:{onvif_port}/onvif/snapshot</tt:Uri>
                <tt:InvalidAfterConnect>true</tt:InvalidAfterConnect>
                <tt:InvalidAfterReboot>false</tt:InvalidAfterReboot>
                <tt:Timeout>PT30S</tt:Timeout>
            </trt:MediaUri>
        </trt:GetSnapshotUriResponse>
    </s:Body>
</s:Envelope>
"""

GET_DIGITAL_INPUTS_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:tt="http://www.onvif.org/ver10/schema" xmlns:tds="http://www.onvif.org/ver10/device/wsdl">
    <s:Body>
        <tds:GetDigitalInputsResponse>
            <tds:DigitalInputs token="doorbell_input"/>
        </tds:GetDigitalInputsResponse>
    </s:Body>
</s:Envelope>
"""

GET_SYSTEM_DATE_AND_TIME_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:tds="http://www.onvif.org/ver10/device/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <tds:GetSystemDateAndTimeResponse>
            <tds:SystemDateAndTime>
                <tt:DateTimeType>Manual</tt:DateTimeType>
                <tt:DaylightSavings>false</tt:DaylightSavings>
                <tt:TimeZone>
                    <tt:TZ>UTC</tt:TZ>
                </tt:TimeZone>
                <tt:UTCDateTime>
                    <tt:Time>
                        <tt:Hour>{hour}</tt:Hour>
                        <tt:Minute>{minute}</tt:Minute>
                        <tt:Second>{second}</tt:Second>
                    </tt:Time>
                    <tt:Date>
                        <tt:Year>{year}</tt:Year>
                        <tt:Month>{month}</tt:Month>
                        <tt:Day>{day}</tt:Day>
                    </tt:Date>
                </tt:UTCDateTime>
            </tds:SystemDateAndTime>
        </tds:GetSystemDateAndTimeResponse>
    </s:Body>
</s:Envelope>
"""

GET_VIDEO_SOURCES_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:trt="http://www.onvif.org/ver10/media/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <trt:GetVideoSourcesResponse>
            <trt:VideoSources token="video_source_token">
                <tt:Framerate>{fps}</tt:Framerate>
                <tt:Resolution>
                    <tt:Width>{width}</tt:Width>
                    <tt:Height>{height}</tt:Height>
                </tt:Resolution>
                <tt:Imaging>
                    <tt:Brightness>50.0</tt:Brightness>
                    <tt:ColorSaturation>50.0</tt:ColorSaturation>
                    <tt:Contrast>50.0</tt:Contrast>
                    <tt:Sharpness>50.0</tt:Sharpness>
                </tt:Imaging>
            </trt:VideoSources>
        </trt:GetVideoSourcesResponse>
    </s:Body>
</s:Envelope>
"""

GET_AUDIO_SOURCES_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:trt="http://www.onvif.org/ver10/media/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <trt:GetAudioSourcesResponse>
            <trt:AudioSources token="audio_source_token">
                <tt:Channels>1</tt:Channels>
            </trt:AudioSources>
        </trt:GetAudioSourcesResponse>
    </s:Body>
</s:Envelope>
"""

GET_AUDIO_OUTPUT_CONFIGURATIONS_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:trt="http://www.onvif.org/ver10/media/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <trt:GetAudioOutputConfigurationsResponse>
            <trt:Configurations token="audio_output_config">
                <trt:Name>PrimaryAudioOutput</trt:Name>
                <trt:UseCount>1</trt:UseCount>
                <trt:OutputToken>audio_output_token</trt:OutputToken>
                <trt:SendPrimacy>rtsp://0.0.0.0/</trt:SendPrimacy>
                <trt:OutputLevel>50</trt:OutputLevel>
            </trt:Configurations>
        </trt:GetAudioOutputConfigurationsResponse>
    </s:Body>
</s:Envelope>
"""

GET_AUDIO_OUTPUT_CONFIGURATION_OPTIONS_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:trt="http://www.onvif.org/ver10/media/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <trt:GetAudioOutputConfigurationOptionsResponse>
            <trt:Options>
                <trt:OutputTokensAvailable>audio_output_token</trt:OutputTokensAvailable>
                <trt:SendPrimacyOptions>rtsp://0.0.0.0/</trt:SendPrimacyOptions>
                <trt:OutputLevelRange>
                    <tt:Min>0</tt:Min>
                    <tt:Max>100</tt:Max>
                </trt:OutputLevelRange>
            </trt:Options>
        </trt:GetAudioOutputConfigurationOptionsResponse>
    </s:Body>
</s:Envelope>
"""

SET_AUDIO_OUTPUT_CONFIGURATION_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope">
    <s:Body>
        <SetAudioOutputConfigurationResponse xmlns="http://www.onvif.org/ver10/media/wsdl"></SetAudioOutputConfigurationResponse>
    </s:Body>
</s:Envelope>
"""

GET_VIDEO_ENCODER_CONFIGURATION_OPTIONS_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:trt="http://www.onvif.org/ver10/media/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <trt:GetVideoEncoderConfigurationOptionsResponse>
            <trt:Options>
                <tt:QualityRange>
                    <tt:Min>1</tt:Min>
                    <tt:Max>5</tt:Max>
                </tt:QualityRange>
                <tt:H264>
                    <tt:ResolutionsAvailable>
                        <tt:Width>{width}</tt:Width>
                        <tt:Height>{height}</tt:Height>
                    </tt:ResolutionsAvailable>
                    <tt:FrameRateRange>
                        <tt:Min>1</tt:Min>
                        <tt:Max>{fps}</tt:Max>
                    </tt:FrameRateRange>
                    <tt:EncodingIntervalRange>
                        <tt:Min>1</tt:Min>
                        <tt:Max>1</tt:Max>
                    </tt:EncodingIntervalRange>
                </tt:H264>
            </trt:Options>
        </trt:GetVideoEncoderConfigurationOptionsResponse>
    </s:Body>
</s:Envelope>
"""

SET_VIDEO_ENCODER_CONFIGURATION_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope">
    <s:Body>
        <SetVideoEncoderConfigurationResponse xmlns="http://www.onvif.org/ver10/media/wsdl"></SetVideoEncoderConfigurationResponse>
    </s:Body>
</s:Envelope>
"""

GET_VIDEO_ENCODER_CONFIGURATION_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:trt="http://www.onvif.org/ver10/media/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <trt:GetVideoEncoderConfigurationResponse>
            <trt:Configuration token="video_encoder_config">
                <trt:Name>video_encoder_config</trt:Name>
                <trt:UseCount>1</trt:UseCount>
                <trt:Encoding>H264</trt:Encoding>
                <trt:Resolution>
                    <tt:Width>{width}</tt:Width>
                    <tt:Height>{height}</tt:Height>
                </trt:Resolution>
                <trt:RateControl>
                    <tt:FrameRateLimit>{fps}</tt:FrameRateLimit>
                    <tt:BitrateLimit>{bitrate}</tt:BitrateLimit>
                </trt:RateControl>
            </trt:Configuration>
        </trt:GetVideoEncoderConfigurationResponse>
    </s:Body>
</s:Envelope>
"""

SET_AUDIO_ENCODER_CONFIGURATION_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope">
    <s:Body>
        <SetAudioEncoderConfigurationResponse xmlns="http://www.onvif.org/ver10/media/wsdl"></SetAudioEncoderConfigurationResponse>
    </s:Body>
</s:Envelope>
"""

GET_AUDIO_ENCODER_CONFIGURATION_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:trt="http://www.onvif.org/ver10/media/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <trt:GetAudioEncoderConfigurationResponse>
            <trt:Configuration token="audio_encoder_config">
                <trt:Name>audio_encoder_config</trt:Name>
                <trt:UseCount>1</trt:UseCount>
                <trt:Encoding>AAC</trt:Encoding>
                <trt:Bitrate>{bitrate}</trt:Bitrate>
                <trt:SampleRate>{samplerate}</trt:SampleRate>
            </trt:Configuration>
        </trt:GetAudioEncoderConfigurationResponse>
    </s:Body>
</s:Envelope>
"""

GET_PROFILES_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope" xmlns:trt="http://www.onvif.org/ver10/media/wsdl" xmlns:tt="http://www.onvif.org/ver10/schema">
    <s:Body>
        <trt:GetProfilesResponse>
            <trt:Profiles token="main_profile" fixed="true">
                <trt:Name>Primary Profile</trt:Name>
                <trt:VideoSourceConfiguration token="video_source_config_token">
                    <trt:Name>PrimaryVideoSource</trt:Name>
                    <trt:UseCount>1</trt:UseCount>
                    <trt:SourceToken>video_source_token</trt:SourceToken>
                    <trt:Bounds x="0" y="0" width="{width}" height="{height}"/>
                </trt:VideoSourceConfiguration>
                <trt:AudioSourceConfiguration token="audio_source_config_token">
                    <trt:Name>PrimaryAudioSource</trt:Name>
                    <trt:UseCount>1</trt:UseCount>
                    <trt:SourceToken>audio_source_token</trt:SourceToken>
                </trt:AudioSourceConfiguration>
                <trt:VideoEncoderConfiguration token="video_encoder_config">
                    <trt:Name>video_encoder_config</trt:Name>
                    <trt:UseCount>1</trt:UseCount>
                    <trt:Encoding>H264</trt:Encoding>
                    <trt:Resolution>
                        <tt:Width>{width}</tt:Width>
                        <tt:Height>{height}</tt:Height>
                    </trt:Resolution>
                    <trt:RateControl>
                        <tt:FrameRateLimit>{fps}</tt:FrameRateLimit>
                        <tt:BitrateLimit>1024</tt:BitrateLimit>
                    </trt:RateControl>
                    <trt:Multicast>
                        <tt:Address>
                            <tt:Type>IPv4</tt:Type>
                            <tt:IPv4Address>0.0.0.0</tt:IPv4Address>
                        </tt:Address>
                        <tt:Port>0</tt:Port>
                        <tt:TTL>0</tt:TTL>
                        <tt:AutoStart>false</tt:AutoStart>
                    </trt:Multicast>
                </trt:VideoEncoderConfiguration>
                <trt:AudioEncoderConfiguration token="audio_encoder_config">
                    <trt:Name>audio_encoder_config</trt:Name>
                    <trt:UseCount>1</trt:UseCount>
                    <trt:Encoding>AAC</trt:Encoding>
                    <trt:Bitrate>32</trt:Bitrate>
                    <trt:SampleRate>16</trt:SampleRate>
                </trt:AudioEncoderConfiguration>
                <trt:AudioOutputConfiguration token="audio_output_config">
                    <trt:Name>PrimaryAudioOutput</trt:Name>
                    <trt:UseCount>1</trt:UseCount>
                    <trt:OutputToken>audio_output_token</trt:OutputToken>
                    <trt:SendPrimacy>rtsp://0.0.0.0/</trt:SendPrimacy>
                    <trt:OutputLevel>50</trt:OutputLevel>
                </trt:AudioOutputConfiguration>
            </trt:Profiles>
        </trt:GetProfilesResponse>
    </s:Body>
</s:Envelope>
"""

GET_URI_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope">
    <s:Body>
        <GetStreamUriResponse xmlns="http://www.onvif.org/ver10/media/wsdl">
            <MediaUri>
                <Uri>rtsp://{server_ip}:8555/doorbell</Uri>
                <InvalidAfterConnect>false</InvalidAfterConnect>
                <InvalidAfterReboot>false</InvalidAfterReboot>
                <Timeout>PT60S</Timeout>
            </MediaUri>
        </GetStreamUriResponse>
    </s:Body>
</s:Envelope>
"""

GENERIC_RESPONSE_TEMPLATE = """<?xml version="1.0" encoding="UTF-8"?>
<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope"><s:Body></s:Body></s:Envelope>
"""