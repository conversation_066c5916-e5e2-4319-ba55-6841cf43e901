import asyncio
import logging
import socket
import xml.etree.ElementTree as ET
from uuid import uuid4
from datetime import datetime, timedelta
from aiohttp import web

import config
from config import (
    ONVIF_PORT, ONVIF_DEVICE_UUID, ONVIF_SERIAL_NUMBER, 
    VIDEO_WIDTH, VIDEO_HEIGHT, DUMMY_FPS, RTSP_PORT,
    STILL_IMAGE_PATH
)
from onvif_templates import *
from utils import get_local_ip


async def handle_onvif(request):
    try:
        # Wrap the entire handler in a timeout to prevent hanging
        return await asyncio.wait_for(_handle_onvif_internal(request), timeout=60.0)
    except asyncio.TimeoutError:
        logging.error("[ONVIF Service] Request handler timed out after 60 seconds")
        return web.Response(text="Request timeout", status=500)


async def _handle_onvif_internal(request):
    host_ip = get_local_ip()
    body = await request.text()

    logging.info(f"[ONVIF Service] Received POST on {request.path}: {body}")

    # Define namespaces to parse requests
    ns = {
        's': 'http://www.w3.org/2003/05/soap-envelope',
        'a': 'http://www.w3.org/2005/08/addressing',
        'tds': 'http://www.onvif.org/ver10/device/wsdl',
        'trt': 'http://www.onvif.org/ver10/media/wsdl',
        'tev': 'http://www.onvif.org/ver10/events/wsdl',
        'wsnt': 'http://docs.oasis-open.org/wsn/b-2',
        'tt': 'http://www.onvif.org/ver10/schema'
    }

    try:
        root = ET.fromstring(body)
    except ET.ParseError:
        root = None

    response_xml = GENERIC_RESPONSE_TEMPLATE

    # Check for the most specific names first to avoid incorrect matching
    if "GetSnapshotUri" in body:
        logging.info("[ONVIF Service] Responding to GetSnapshotUri")
        response_xml = GET_SNAPSHOT_URI_RESPONSE_TEMPLATE.format(server_ip=host_ip, onvif_port=ONVIF_PORT)
    elif "GetEventProperties" in body:
        logging.info("[ONVIF Service] Responding to GetEventProperties")
        response_xml = GET_EVENT_PROPERTIES_RESPONSE_TEMPLATE
    elif "CreatePullPointSubscription" in body:
        logging.info("[ONVIF Service] Responding to CreatePullPointSubscription")
        now = datetime.utcnow()
        termination_time = now + timedelta(minutes=10)
        relates_to_element = root.find('.//a:MessageID', ns)
        relates_to = relates_to_element.text if relates_to_element is not None else "urn:uuid:" + str(uuid4())
        response_xml = CREATE_PULL_POINT_SUBSCRIPTION_RESPONSE_TEMPLATE.format(
            relates_to=relates_to,
            server_ip=host_ip,
            onvif_port=ONVIF_PORT,
            current_time=now.isoformat() + "Z",
            termination_time=termination_time.isoformat() + "Z"
        )
    elif "PullMessages" in body:
        logging.info("[ONVIF Service] Handling PullMessages request...")
        
        # Check for immediate events first
        notification_messages = ""
        while not config.g_event_queue.empty():
            try:
                notification_messages += config.g_event_queue.get_nowait()
            except asyncio.QueueEmpty:
                break
        
        # If no immediate events, use polling approach to avoid blocking the event loop
        if not notification_messages:
            logging.info("[ONVIF Service] No events, using polling approach...")
            try:
                # Use polling instead of event.wait() to avoid blocking
                poll_count = 0
                max_polls = 150  # 150 * 0.2s = 30 seconds total
                
                while poll_count < max_polls:
                    # Check if events are available
                    if config.g_event_available.is_set():
                        # Check queue for new events
                        while not config.g_event_queue.empty():
                            try:
                                notification_messages += config.g_event_queue.get_nowait()
                            except asyncio.QueueEmpty:
                                break
                        # Clear the event flag and break if we found events
                        config.g_event_available.clear()
                        if notification_messages:
                            break
                    
                    # Short sleep to yield control to other tasks
                    await asyncio.sleep(0.2)
                    poll_count += 1
                    
                    # Debug: Log every 50 polls (10 seconds)
                    if poll_count % 50 == 0:
                        logging.info(f"[ONVIF Service] Long poll progress: {poll_count}/150 polls ({poll_count * 0.2:.1f}s)")
                
                if poll_count >= max_polls:
                    logging.info("[ONVIF Service] Long poll timed out, no new events.")
                else:
                    logging.info(f"[ONVIF Service] Long poll completed after {poll_count} polls ({poll_count * 0.2:.1f}s)")
                    
            except asyncio.CancelledError:
                logging.info("[ONVIF Service] Long poll cancelled.")
                raise
            except Exception as e:
                logging.error(f"[ONVIF Service] Error during long poll: {e}")
            finally:
                # Always clear the event flag
                config.g_event_available.clear()
        
        now = datetime.utcnow()
        termination_time = now + timedelta(minutes=10)

        if notification_messages:
            logging.info(f"[ONVIF Service] Sending event notification(s)...")
            response_xml = PULL_MESSAGES_RESPONSE_WITH_NOTIFICATION_TEMPLATE.format(
                current_time=now.isoformat() + "Z",
                termination_time=termination_time.isoformat() + "Z",
                notification_messages=notification_messages
            )
        else:
            response_xml = PULL_MESSAGES_RESPONSE_TEMPLATE.format(
                current_time=now.isoformat() + "Z",
                termination_time=termination_time.isoformat() + "Z"
            )
    elif "Renew" in body:
        logging.info("[ONVIF Service] Responding to Renew")
        now = datetime.utcnow()
        termination_time = now + timedelta(minutes=10)
        response_xml = RENEW_RESPONSE_TEMPLATE.format(
            current_time=now.isoformat() + "Z",
            termination_time=termination_time.isoformat() + "Z"
        )
    elif "GetDigitalInputs" in body:
        logging.info("[ONVIF Service] Responding to GetDigitalInputs")
        response_xml = GET_DIGITAL_INPUTS_RESPONSE_TEMPLATE
    elif "GetVideoEncoderConfigurationOptions" in body:
        logging.info("[ONVIF Service] Responding to GetVideoEncoderConfigurationOptions")
        response_xml = GET_VIDEO_ENCODER_CONFIGURATION_OPTIONS_RESPONSE_TEMPLATE.format(width=VIDEO_WIDTH, height=VIDEO_HEIGHT, fps=DUMMY_FPS)
    elif "SetVideoEncoderConfiguration" in body:
        logging.info("[ONVIF Service] Responding to SetVideoEncoderConfiguration")
        response_xml = """<s:Envelope xmlns:s="http://www.w3.org/2003/05/soap-envelope"><s:Body><SetVideoEncoderConfigurationResponse xmlns="http://www.onvif.org/ver10/media/wsdl"></SetVideoEncoderConfigurationResponse></s:Body></s:Envelope>"""
    elif "GetVideoEncoderConfiguration" in body:
        logging.info("[ONVIF Service] Responding to GetVideoEncoderConfiguration")
        response_xml = GET_VIDEO_ENCODER_CONFIGURATION_RESPONSE_TEMPLATE.format(width=VIDEO_WIDTH, height=VIDEO_HEIGHT, fps=DUMMY_FPS, bitrate=1024)
    elif "SetAudioEncoderConfiguration" in body:
        logging.info("[ONVIF Service] Responding to SetAudioEncoderConfiguration")
        response_xml = SET_AUDIO_ENCODER_CONFIGURATION_RESPONSE_TEMPLATE.format()
    elif "GetAudioEncoderConfiguration" in body:
        logging.info("[ONVIF Service] Responding to GetAudioEncoderConfiguration")
        response_xml = GET_AUDIO_ENCODER_CONFIGURATION_RESPONSE_TEMPLATE.format(bitrate=32, samplerate=16)
    elif "GetDeviceInformation" in body:
        logging.info("[ONVIF Service] Responding to GetDeviceInformation")
        response_xml = GET_DEVICE_INFORMATION_RESPONSE_TEMPLATE.format(serial_number=ONVIF_SERIAL_NUMBER)
    elif "GetServiceCapabilities" in body:
        logging.info("[ONVIF Service] Responding to GetServiceCapabilities")
        response_xml = GET_SERVICE_CAPABILITIES_RESPONSE_TEMPLATE.format()
    elif "GetServices" in body:
        logging.info("[ONVIF Service] Responding to GetServices")
        response_xml = GET_SERVICES_RESPONSE_TEMPLATE.format(server_ip=host_ip, onvif_port=ONVIF_PORT)
    elif "GetCapabilities" in body:
        logging.info("[ONVIF Service] Responding to GetCapabilities")
        response_xml = GET_CAPABILITIES_RESPONSE_TEMPLATE.format(server_ip=host_ip, onvif_port=ONVIF_PORT)
    elif "GetVideoSources" in body:
        logging.info("[ONVIF Service] Responding to GetVideoSources")
        response_xml = GET_VIDEO_SOURCES_RESPONSE_TEMPLATE.format(fps=DUMMY_FPS, width=VIDEO_WIDTH, height=VIDEO_HEIGHT)
    elif "GetAudioSources" in body:
        logging.info("[ONVIF Service] Responding to GetAudioSources")
        response_xml = GET_AUDIO_SOURCES_RESPONSE_TEMPLATE.format()
    elif "GetAudioOutputConfigurations" in body:
        logging.info("[ONVIF Service] Responding to GetAudioOutputConfigurations")
        response_xml = GET_AUDIO_OUTPUT_CONFIGURATIONS_RESPONSE_TEMPLATE.format()
    elif "GetAudioOutputConfigurationOptions" in body:
        logging.info("[ONVIF Service] Responding to GetAudioOutputConfigurationOptions")
        response_xml = GET_AUDIO_OUTPUT_CONFIGURATION_OPTIONS_RESPONSE_TEMPLATE.format()
    elif "SetAudioOutputConfiguration" in body:
        logging.info("[ONVIF Service] Responding to SetAudioOutputConfiguration")
        response_xml = SET_AUDIO_OUTPUT_CONFIGURATION_RESPONSE_TEMPLATE.format()
    elif "GetProfiles" in body:
        logging.info("[ONVIF Service] Responding to GetProfiles")
        response_xml = GET_PROFILES_RESPONSE_TEMPLATE.format(width=VIDEO_WIDTH, height=VIDEO_HEIGHT, fps=DUMMY_FPS)
    elif "GetStreamUri" in body:
        logging.info("[ONVIF Service] Responding to GetStreamUri")
        response_xml = GET_URI_RESPONSE_TEMPLATE.format(server_ip=host_ip, rtsp_port=RTSP_PORT)
    elif "GetSystemDateAndTime" in body:
        logging.info("[ONVIF Service] Responding to GetSystemDateAndTime")
        now = datetime.utcnow()
        response_xml = GET_SYSTEM_DATE_AND_TIME_RESPONSE_TEMPLATE.format(year=now.year, month=now.month, day=now.day, hour=now.hour, minute=now.minute, second=now.second)
    else:
        logging.info(f"[ONVIF Service] Responding with generic success to unhandled request: {body[:200]}")
        response_xml = GENERIC_RESPONSE_TEMPLATE

    return web.Response(text=response_xml, content_type='application/soap+xml', charset='utf-8')


async def handle_snapshot(request):
    import time
    
    # Check if we should use doorbell snapshot (time-based)
    if config.doorbell_snapshot_until and time.time() < config.doorbell_snapshot_until:
        logging.info("Snapshot requested. Returning doorbell captured image.")
        image_path = "last_call.png"
        content_type = "image/png"
    else:
        logging.info("Snapshot requested. Returning normal image.")
        image_path = STILL_IMAGE_PATH  
        content_type = "image/jpeg"
        # Clear the timestamp if it has expired
        if config.doorbell_snapshot_until:
            config.doorbell_snapshot_until = None
    
    try:
        with open(image_path, "rb") as f:
            return web.Response(body=f.read(), content_type=content_type)
    except FileNotFoundError:
        return web.Response(text=f"Image {image_path} not found.", status=404)


class OnvifDiscoveryProtocol(asyncio.DatagramProtocol):
    def __init__(self, response_template, server_ip):
        self.response_template = response_template
        self.server_ip = server_ip
        self.transport = None

    def connection_made(self, transport):
        self.transport = transport
        sock = self.transport.get_extra_info('socket')
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP,
                        socket.inet_aton("***************") + socket.inet_aton("0.0.0.0"))

    def datagram_received(self, data, addr):
        if b"Probe" not in data:
            return

        logging.info(f"[WS-Discovery] Received Probe from {addr}")
        relates_to = "urn:uuid:" + str(uuid4())
        try:
            relates_to = data.decode().split("<a:MessageID>")[1].split("</a:MessageID>")[0]
        except IndexError:
            pass

        response = self.response_template.format(
            message_id="urn:uuid:" + str(uuid4()),
            relates_to=relates_to,
            device_uuid=ONVIF_DEVICE_UUID,
            server_ip=self.server_ip,
            onvif_port=ONVIF_PORT
        ).encode('utf-8')

        self.transport.sendto(response, addr)
        logging.info(f"[WS-Discovery] Sent ProbeMatch to {addr}")

    def error_received(self, exc):
        logging.error(f'[WS-Discovery] Error received: {exc}')

    def connection_lost(self, exc):
        logging.info('[WS-Discovery] Socket closed, stopping.')


async def start_onvif_discovery(ip):
    logging.info("Starting ONVIF WS-Discovery service...")
    loop = asyncio.get_running_loop()

    try:
        transport, protocol = await loop.create_datagram_endpoint(
            lambda: OnvifDiscoveryProtocol(PROBE_MATCH_TEMPLATE, ip),
            local_addr=('0.0.0.0', 3702)
        )
        logging.info("[WS-Discovery] Listening for probes on multicast ***************:3702")
        # Use a cancellable wait instead of infinite wait
        while True:
            try:
                await asyncio.sleep(10)  # Check for cancellation every 10 seconds
            except asyncio.CancelledError:
                break
    except asyncio.CancelledError:
        logging.info("[WS-Discovery] Discovery service cancelled")
        pass
    except Exception as e:
        logging.error(f"Failed to start ONVIF Discovery: {e}", exc_info=True)
    finally:
        if 'transport' in locals() and not transport.is_closing():
            transport.close()
        logging.info("ONVIF WS-Discovery service stopped.")