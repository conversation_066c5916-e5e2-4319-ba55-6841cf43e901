package rtpmjpeg

import (
	"bytes"
	"testing"

	"github.com/stretchr/testify/require"
)

var casesQuantizationTable = []struct {
	name string
	enc  []byte
	dec  headerQuantizationTable
}{
	{
		"base",
		[]byte{
			0x1, 0x0, 0x0, 0x40, 0x1, 0x2, 0x3, 0x4,
			0x1, 0x2, 0x3, 0x4, 0x1, 0x2, 0x3, 0x4,
			0x1, 0x2, 0x3, 0x4, 0x1, 0x2, 0x3, 0x4,
			0x1, 0x2, 0x3, 0x4, 0x1, 0x2, 0x3, 0x4,
			0x1, 0x2, 0x3, 0x4, 0x1, 0x2, 0x3, 0x4,
			0x1, 0x2, 0x3, 0x4, 0x1, 0x2, 0x3, 0x4,
			0x1, 0x2, 0x3, 0x4, 0x1, 0x2, 0x3, 0x4,
			0x1, 0x2, 0x3, 0x4, 0x1, 0x2, 0x3, 0x4,
			0x1, 0x2, 0x3, 0x4,
		},
		headerQuantizationTable{
			MBZ:       1,
			Precision: 0,
			Tables:    [][]byte{bytes.Repeat([]byte{0x01, 0x02, 0x03, 0x04}, 64/4)},
		},
	},
}

func TestHeaderQuantizationTableUnmarshal(t *testing.T) {
	for _, ca := range casesQuantizationTable {
		t.Run(ca.name, func(t *testing.T) {
			var h headerQuantizationTable
			_, err := h.unmarshal(ca.enc)
			require.NoError(t, err)
			require.Equal(t, ca.dec, h)
		})
	}
}

func TestHeaderQuantizationTableMarshal(t *testing.T) {
	for _, ca := range casesQuantizationTable {
		t.Run(ca.name, func(t *testing.T) {
			buf := ca.dec.marshal(nil)
			require.Equal(t, ca.enc, buf)
		})
	}
}
