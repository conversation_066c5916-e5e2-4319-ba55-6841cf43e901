package format

import (
	"testing"

	"github.com/pion/rtp"
	"github.com/stretchr/testify/require"
)

func TestAC3Attributes(t *testing.T) {
	format := &AC3{
		PayloadTyp:   96,
		SampleRate:   44100,
		ChannelCount: 2,
	}
	require.Equal(t, "AC-3", format.Codec())
	require.Equal(t, 44100, format.ClockRate())
	require.Equal(t, true, format.PTSEqualsDTS(&rtp.Packet{}))
}

func TestAC3DecEncoder(t *testing.T) {
	format := &AC3{
		PayloadTyp:   96,
		SampleRate:   44100,
		ChannelCount: 2,
	}

	enc, err := format.CreateEncoder()
	require.NoError(t, err)

	pkts, err := enc.Encode([][]byte{{ //nolint:dupl
		0x0b, 0x77, 0x47, 0x11, 0x0c, 0x40, 0x2f, 0x84,
		0x2b, 0xc1, 0x07, 0x7a, 0xb0, 0xfa, 0xbb, 0xea,
		0xef, 0x9f, 0x57, 0x7c, 0xf9, 0xf3, 0xf7, 0xcf,
		0x9f, 0x3e, 0x32, 0xfe, 0xd5, 0xc1, 0x50, 0xde,
		0xc5, 0x1e, 0x73, 0xd2, 0x6c, 0xa6, 0x94, 0x46,
		0x4e, 0x92, 0x8c, 0x0f, 0xb9, 0xcf, 0xad, 0x07,
		0x54, 0x4a, 0x2e, 0xf3, 0x7d, 0x07, 0x2e, 0xa4,
		0x2f, 0xba, 0xbf, 0x39, 0xb5, 0xc9, 0x92, 0xa6,
		0xe1, 0xb4, 0x70, 0xc5, 0xc4, 0xb5, 0xe6, 0x5d,
		0x0f, 0xa8, 0x71, 0xa4, 0xcc, 0xc5, 0xbc, 0x75,
		0x67, 0x92, 0x52, 0x4f, 0x7e, 0x62, 0x1c, 0xa9,
		0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0, 0x44, 0x92,
		0x30, 0x3b, 0xf7, 0x61, 0xd6, 0x49, 0x96, 0x66,
		0x98, 0x28, 0x1a, 0x95, 0xa9, 0x42, 0xad, 0xb7,
		0x50, 0x90, 0xad, 0x1c, 0x34, 0x80, 0xe2, 0xef,
		0xcd, 0x41, 0x0b, 0xf0, 0x9d, 0x57, 0x62, 0x78,
		0xfd, 0xc6, 0xc2, 0x19, 0x9e, 0x26, 0x31, 0xca,
		0x1e, 0x75, 0xb1, 0x7a, 0x8e, 0xb5, 0x51, 0x3a,
		0xfe, 0xe4, 0xf1, 0x0b, 0x4f, 0x14, 0x90, 0xdb,
		0x9f, 0x44, 0x50, 0xbb, 0xef, 0x74, 0x00, 0x8c,
		0x1f, 0x97, 0xa1, 0xa2, 0xfa, 0x72, 0x16, 0x47,
		0xc6, 0xc0, 0xe5, 0xfe, 0x67, 0x03, 0x9c, 0xfe,
		0x62, 0x01, 0xa1, 0x00, 0x5d, 0xff, 0xa5, 0x03,
		0x59, 0xfa, 0xa8, 0x25, 0x5f, 0x6b, 0x83, 0x51,
		0xf2, 0xc0, 0x44, 0xff, 0x2d, 0x05, 0x4b, 0xee,
		0xe0, 0x54, 0x9e, 0xae, 0x86, 0x45, 0xf3, 0xbd,
		0x0e, 0x42, 0xf2, 0xbf, 0x0f, 0x7f, 0xc6, 0x09,
		0x07, 0xdc, 0x22, 0x11, 0x77, 0xbe, 0x31, 0x27,
		0x5b, 0xa4, 0x13, 0x47, 0x07, 0x32, 0x9f, 0x1f,
		0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d, 0xf3, 0xe7,
		0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3, 0xe7, 0xcf,
		0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f,
		0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3f,
		0x53, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
		0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9,
		0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3,
		0xe7, 0xcf, 0x9f, 0x3e, 0x00, 0x46, 0x28, 0x26,
		0x20, 0x4a, 0x5a, 0xc0, 0x8a, 0xc5, 0xae, 0xa0,
		0x55, 0x78, 0x82, 0x7a, 0x38, 0x10, 0x09, 0xc9,
		0xb8, 0x0c, 0xfa, 0x5b, 0xc9, 0xd2, 0xec, 0x44,
		0x25, 0xf8, 0x20, 0xf2, 0xc8, 0x8a, 0xe9, 0x40,
		0x18, 0x06, 0xc6, 0x2b, 0xc8, 0xed, 0x8f, 0x33,
		0x09, 0x92, 0x28, 0x1e, 0xc4, 0x24, 0xd8, 0x33,
		0xa5, 0x00, 0xf5, 0xea, 0x18, 0xfa, 0x90, 0x97,
		0x97, 0xe8, 0x39, 0x6a, 0xcf, 0xf1, 0xdd, 0xff,
		0x9e, 0x8e, 0x04, 0x02, 0xae, 0x65, 0x87, 0x5c,
		0x4e, 0x72, 0xfd, 0x3c, 0x01, 0x86, 0xfe, 0x56,
		0x59, 0x74, 0x44, 0x3a, 0x40, 0x00, 0xec, 0xfc,
	}})
	require.NoError(t, err)
	require.Equal(t, format.PayloadType(), pkts[0].PayloadType)

	dec, err := format.CreateDecoder()
	require.NoError(t, err)

	byts, err := dec.Decode(pkts[0])
	require.NoError(t, err)
	require.Equal(t, [][]byte{{ //nolint:dupl
		0x0b, 0x77, 0x47, 0x11, 0x0c, 0x40, 0x2f, 0x84,
		0x2b, 0xc1, 0x07, 0x7a, 0xb0, 0xfa, 0xbb, 0xea,
		0xef, 0x9f, 0x57, 0x7c, 0xf9, 0xf3, 0xf7, 0xcf,
		0x9f, 0x3e, 0x32, 0xfe, 0xd5, 0xc1, 0x50, 0xde,
		0xc5, 0x1e, 0x73, 0xd2, 0x6c, 0xa6, 0x94, 0x46,
		0x4e, 0x92, 0x8c, 0x0f, 0xb9, 0xcf, 0xad, 0x07,
		0x54, 0x4a, 0x2e, 0xf3, 0x7d, 0x07, 0x2e, 0xa4,
		0x2f, 0xba, 0xbf, 0x39, 0xb5, 0xc9, 0x92, 0xa6,
		0xe1, 0xb4, 0x70, 0xc5, 0xc4, 0xb5, 0xe6, 0x5d,
		0x0f, 0xa8, 0x71, 0xa4, 0xcc, 0xc5, 0xbc, 0x75,
		0x67, 0x92, 0x52, 0x4f, 0x7e, 0x62, 0x1c, 0xa9,
		0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0, 0x44, 0x92,
		0x30, 0x3b, 0xf7, 0x61, 0xd6, 0x49, 0x96, 0x66,
		0x98, 0x28, 0x1a, 0x95, 0xa9, 0x42, 0xad, 0xb7,
		0x50, 0x90, 0xad, 0x1c, 0x34, 0x80, 0xe2, 0xef,
		0xcd, 0x41, 0x0b, 0xf0, 0x9d, 0x57, 0x62, 0x78,
		0xfd, 0xc6, 0xc2, 0x19, 0x9e, 0x26, 0x31, 0xca,
		0x1e, 0x75, 0xb1, 0x7a, 0x8e, 0xb5, 0x51, 0x3a,
		0xfe, 0xe4, 0xf1, 0x0b, 0x4f, 0x14, 0x90, 0xdb,
		0x9f, 0x44, 0x50, 0xbb, 0xef, 0x74, 0x00, 0x8c,
		0x1f, 0x97, 0xa1, 0xa2, 0xfa, 0x72, 0x16, 0x47,
		0xc6, 0xc0, 0xe5, 0xfe, 0x67, 0x03, 0x9c, 0xfe,
		0x62, 0x01, 0xa1, 0x00, 0x5d, 0xff, 0xa5, 0x03,
		0x59, 0xfa, 0xa8, 0x25, 0x5f, 0x6b, 0x83, 0x51,
		0xf2, 0xc0, 0x44, 0xff, 0x2d, 0x05, 0x4b, 0xee,
		0xe0, 0x54, 0x9e, 0xae, 0x86, 0x45, 0xf3, 0xbd,
		0x0e, 0x42, 0xf2, 0xbf, 0x0f, 0x7f, 0xc6, 0x09,
		0x07, 0xdc, 0x22, 0x11, 0x77, 0xbe, 0x31, 0x27,
		0x5b, 0xa4, 0x13, 0x47, 0x07, 0x32, 0x9f, 0x1f,
		0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d, 0xf3, 0xe7,
		0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3, 0xe7, 0xcf,
		0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f,
		0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3f,
		0x53, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
		0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9,
		0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3,
		0xe7, 0xcf, 0x9f, 0x3e, 0x00, 0x46, 0x28, 0x26,
		0x20, 0x4a, 0x5a, 0xc0, 0x8a, 0xc5, 0xae, 0xa0,
		0x55, 0x78, 0x82, 0x7a, 0x38, 0x10, 0x09, 0xc9,
		0xb8, 0x0c, 0xfa, 0x5b, 0xc9, 0xd2, 0xec, 0x44,
		0x25, 0xf8, 0x20, 0xf2, 0xc8, 0x8a, 0xe9, 0x40,
		0x18, 0x06, 0xc6, 0x2b, 0xc8, 0xed, 0x8f, 0x33,
		0x09, 0x92, 0x28, 0x1e, 0xc4, 0x24, 0xd8, 0x33,
		0xa5, 0x00, 0xf5, 0xea, 0x18, 0xfa, 0x90, 0x97,
		0x97, 0xe8, 0x39, 0x6a, 0xcf, 0xf1, 0xdd, 0xff,
		0x9e, 0x8e, 0x04, 0x02, 0xae, 0x65, 0x87, 0x5c,
		0x4e, 0x72, 0xfd, 0x3c, 0x01, 0x86, 0xfe, 0x56,
		0x59, 0x74, 0x44, 0x3a, 0x40, 0x00, 0xec, 0xfc,
	}}, byts)
}
