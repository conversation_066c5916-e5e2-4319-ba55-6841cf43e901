//nolint:dupl
package rtpac3

import (
	"testing"

	"github.com/pion/rtp"
	"github.com/stretchr/testify/require"
)

func uint16Ptr(v uint16) *uint16 {
	return &v
}

func uint32Ptr(v uint32) *uint32 {
	return &v
}

var cases = []struct {
	name   string
	frames [][]byte
	pkts   []*rtp.Packet
}{
	{
		"single",
		[][]byte{{
			0x0b, 0x77, 0x47, 0x11, 0x0c, 0x40, 0x2f, 0x84,
			0x2b, 0xc1, 0x07, 0x7a, 0xb0, 0xfa, 0xbb, 0xea,
			0xef, 0x9f, 0x57, 0x7c, 0xf9, 0xf3, 0xf7, 0xcf,
			0x9f, 0x3e, 0x32, 0xfe, 0xd5, 0xc1, 0x50, 0xde,
			0xc5, 0x1e, 0x73, 0xd2, 0x6c, 0xa6, 0x94, 0x46,
			0x4e, 0x92, 0x8c, 0x0f, 0xb9, 0xcf, 0xad, 0x07,
			0x54, 0x4a, 0x2e, 0xf3, 0x7d, 0x07, 0x2e, 0xa4,
			0x2f, 0xba, 0xbf, 0x39, 0xb5, 0xc9, 0x92, 0xa6,
			0xe1, 0xb4, 0x70, 0xc5, 0xc4, 0xb5, 0xe6, 0x5d,
			0x0f, 0xa8, 0x71, 0xa4, 0xcc, 0xc5, 0xbc, 0x75,
			0x67, 0x92, 0x52, 0x4f, 0x7e, 0x62, 0x1c, 0xa9,
			0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0, 0x44, 0x92,
			0x30, 0x3b, 0xf7, 0x61, 0xd6, 0x49, 0x96, 0x66,
			0x98, 0x28, 0x1a, 0x95, 0xa9, 0x42, 0xad, 0xb7,
			0x50, 0x90, 0xad, 0x1c, 0x34, 0x80, 0xe2, 0xef,
			0xcd, 0x41, 0x0b, 0xf0, 0x9d, 0x57, 0x62, 0x78,
			0xfd, 0xc6, 0xc2, 0x19, 0x9e, 0x26, 0x31, 0xca,
			0x1e, 0x75, 0xb1, 0x7a, 0x8e, 0xb5, 0x51, 0x3a,
			0xfe, 0xe4, 0xf1, 0x0b, 0x4f, 0x14, 0x90, 0xdb,
			0x9f, 0x44, 0x50, 0xbb, 0xef, 0x74, 0x00, 0x8c,
			0x1f, 0x97, 0xa1, 0xa2, 0xfa, 0x72, 0x16, 0x47,
			0xc6, 0xc0, 0xe5, 0xfe, 0x67, 0x03, 0x9c, 0xfe,
			0x62, 0x01, 0xa1, 0x00, 0x5d, 0xff, 0xa5, 0x03,
			0x59, 0xfa, 0xa8, 0x25, 0x5f, 0x6b, 0x83, 0x51,
			0xf2, 0xc0, 0x44, 0xff, 0x2d, 0x05, 0x4b, 0xee,
			0xe0, 0x54, 0x9e, 0xae, 0x86, 0x45, 0xf3, 0xbd,
			0x0e, 0x42, 0xf2, 0xbf, 0x0f, 0x7f, 0xc6, 0x09,
			0x07, 0xdc, 0x22, 0x11, 0x77, 0xbe, 0x31, 0x27,
			0x5b, 0xa4, 0x13, 0x47, 0x07, 0x32, 0x9f, 0x1f,
			0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d, 0xf3, 0xe7,
			0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3, 0xe7, 0xcf,
			0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f,
			0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3f,
			0x53, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
			0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9,
			0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3,
			0xe7, 0xcf, 0x9f, 0x3e, 0x00, 0x46, 0x28, 0x26,
			0x20, 0x4a, 0x5a, 0xc0, 0x8a, 0xc5, 0xae, 0xa0,
			0x55, 0x78, 0x82, 0x7a, 0x38, 0x10, 0x09, 0xc9,
			0xb8, 0x0c, 0xfa, 0x5b, 0xc9, 0xd2, 0xec, 0x44,
			0x25, 0xf8, 0x20, 0xf2, 0xc8, 0x8a, 0xe9, 0x40,
			0x18, 0x06, 0xc6, 0x2b, 0xc8, 0xed, 0x8f, 0x33,
			0x09, 0x92, 0x28, 0x1e, 0xc4, 0x24, 0xd8, 0x33,
			0xa5, 0x00, 0xf5, 0xea, 0x18, 0xfa, 0x90, 0x97,
			0x97, 0xe8, 0x39, 0x6a, 0xcf, 0xf1, 0xdd, 0xff,
			0x9e, 0x8e, 0x04, 0x02, 0xae, 0x65, 0x87, 0x5c,
			0x4e, 0x72, 0xfd, 0x3c, 0x01, 0x86, 0xfe, 0x56,
			0x59, 0x74, 0x44, 0x3a, 0x40, 0x00, 0xec, 0xfc,
		}},
		[]*rtp.Packet{
			{
				Header: rtp.Header{
					Version:        2,
					Marker:         true,
					PayloadType:    96,
					SequenceNumber: 17645,
					SSRC:           0x9dbb7812,
				},
				Payload: []byte{
					0x00, 0x01, 0x0b, 0x77, 0x47, 0x11, 0x0c, 0x40,
					0x2f, 0x84, 0x2b, 0xc1, 0x07, 0x7a, 0xb0, 0xfa,
					0xbb, 0xea, 0xef, 0x9f, 0x57, 0x7c, 0xf9, 0xf3,
					0xf7, 0xcf, 0x9f, 0x3e, 0x32, 0xfe, 0xd5, 0xc1,
					0x50, 0xde, 0xc5, 0x1e, 0x73, 0xd2, 0x6c, 0xa6,
					0x94, 0x46, 0x4e, 0x92, 0x8c, 0x0f, 0xb9, 0xcf,
					0xad, 0x07, 0x54, 0x4a, 0x2e, 0xf3, 0x7d, 0x07,
					0x2e, 0xa4, 0x2f, 0xba, 0xbf, 0x39, 0xb5, 0xc9,
					0x92, 0xa6, 0xe1, 0xb4, 0x70, 0xc5, 0xc4, 0xb5,
					0xe6, 0x5d, 0x0f, 0xa8, 0x71, 0xa4, 0xcc, 0xc5,
					0xbc, 0x75, 0x67, 0x92, 0x52, 0x4f, 0x7e, 0x62,
					0x1c, 0xa9, 0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0,
					0x44, 0x92, 0x30, 0x3b, 0xf7, 0x61, 0xd6, 0x49,
					0x96, 0x66, 0x98, 0x28, 0x1a, 0x95, 0xa9, 0x42,
					0xad, 0xb7, 0x50, 0x90, 0xad, 0x1c, 0x34, 0x80,
					0xe2, 0xef, 0xcd, 0x41, 0x0b, 0xf0, 0x9d, 0x57,
					0x62, 0x78, 0xfd, 0xc6, 0xc2, 0x19, 0x9e, 0x26,
					0x31, 0xca, 0x1e, 0x75, 0xb1, 0x7a, 0x8e, 0xb5,
					0x51, 0x3a, 0xfe, 0xe4, 0xf1, 0x0b, 0x4f, 0x14,
					0x90, 0xdb, 0x9f, 0x44, 0x50, 0xbb, 0xef, 0x74,
					0x00, 0x8c, 0x1f, 0x97, 0xa1, 0xa2, 0xfa, 0x72,
					0x16, 0x47, 0xc6, 0xc0, 0xe5, 0xfe, 0x67, 0x03,
					0x9c, 0xfe, 0x62, 0x01, 0xa1, 0x00, 0x5d, 0xff,
					0xa5, 0x03, 0x59, 0xfa, 0xa8, 0x25, 0x5f, 0x6b,
					0x83, 0x51, 0xf2, 0xc0, 0x44, 0xff, 0x2d, 0x05,
					0x4b, 0xee, 0xe0, 0x54, 0x9e, 0xae, 0x86, 0x45,
					0xf3, 0xbd, 0x0e, 0x42, 0xf2, 0xbf, 0x0f, 0x7f,
					0xc6, 0x09, 0x07, 0xdc, 0x22, 0x11, 0x77, 0xbe,
					0x31, 0x27, 0x5b, 0xa4, 0x13, 0x47, 0x07, 0x32,
					0x9f, 0x1f, 0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d,
					0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3,
					0xe7, 0xcf, 0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7,
					0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf,
					0x9f, 0x3f, 0x53, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f,
					0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e,
					0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
					0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x00, 0x46,
					0x28, 0x26, 0x20, 0x4a, 0x5a, 0xc0, 0x8a, 0xc5,
					0xae, 0xa0, 0x55, 0x78, 0x82, 0x7a, 0x38, 0x10,
					0x09, 0xc9, 0xb8, 0x0c, 0xfa, 0x5b, 0xc9, 0xd2,
					0xec, 0x44, 0x25, 0xf8, 0x20, 0xf2, 0xc8, 0x8a,
					0xe9, 0x40, 0x18, 0x06, 0xc6, 0x2b, 0xc8, 0xed,
					0x8f, 0x33, 0x09, 0x92, 0x28, 0x1e, 0xc4, 0x24,
					0xd8, 0x33, 0xa5, 0x00, 0xf5, 0xea, 0x18, 0xfa,
					0x90, 0x97, 0x97, 0xe8, 0x39, 0x6a, 0xcf, 0xf1,
					0xdd, 0xff, 0x9e, 0x8e, 0x04, 0x02, 0xae, 0x65,
					0x87, 0x5c, 0x4e, 0x72, 0xfd, 0x3c, 0x01, 0x86,
					0xfe, 0x56, 0x59, 0x74, 0x44, 0x3a, 0x40, 0x00,
					0xec, 0xfc,
				},
			},
		},
	},
	{
		"aggregated",
		[][]byte{
			{
				0x0b, 0x77, 0x47, 0x11, 0x0c, 0x40, 0x2f, 0x84,
				0x2b, 0xc1, 0x07, 0x7a, 0xb0, 0xfa, 0xbb, 0xea,
				0xef, 0x9f, 0x57, 0x7c, 0xf9, 0xf3, 0xf7, 0xcf,
				0x9f, 0x3e, 0x32, 0xfe, 0xd5, 0xc1, 0x50, 0xde,
				0xc5, 0x1e, 0x73, 0xd2, 0x6c, 0xa6, 0x94, 0x46,
				0x4e, 0x92, 0x8c, 0x0f, 0xb9, 0xcf, 0xad, 0x07,
				0x54, 0x4a, 0x2e, 0xf3, 0x7d, 0x07, 0x2e, 0xa4,
				0x2f, 0xba, 0xbf, 0x39, 0xb5, 0xc9, 0x92, 0xa6,
				0xe1, 0xb4, 0x70, 0xc5, 0xc4, 0xb5, 0xe6, 0x5d,
				0x0f, 0xa8, 0x71, 0xa4, 0xcc, 0xc5, 0xbc, 0x75,
				0x67, 0x92, 0x52, 0x4f, 0x7e, 0x62, 0x1c, 0xa9,
				0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0, 0x44, 0x92,
				0x30, 0x3b, 0xf7, 0x61, 0xd6, 0x49, 0x96, 0x66,
				0x98, 0x28, 0x1a, 0x95, 0xa9, 0x42, 0xad, 0xb7,
				0x50, 0x90, 0xad, 0x1c, 0x34, 0x80, 0xe2, 0xef,
				0xcd, 0x41, 0x0b, 0xf0, 0x9d, 0x57, 0x62, 0x78,
				0xfd, 0xc6, 0xc2, 0x19, 0x9e, 0x26, 0x31, 0xca,
				0x1e, 0x75, 0xb1, 0x7a, 0x8e, 0xb5, 0x51, 0x3a,
				0xfe, 0xe4, 0xf1, 0x0b, 0x4f, 0x14, 0x90, 0xdb,
				0x9f, 0x44, 0x50, 0xbb, 0xef, 0x74, 0x00, 0x8c,
				0x1f, 0x97, 0xa1, 0xa2, 0xfa, 0x72, 0x16, 0x47,
				0xc6, 0xc0, 0xe5, 0xfe, 0x67, 0x03, 0x9c, 0xfe,
				0x62, 0x01, 0xa1, 0x00, 0x5d, 0xff, 0xa5, 0x03,
				0x59, 0xfa, 0xa8, 0x25, 0x5f, 0x6b, 0x83, 0x51,
				0xf2, 0xc0, 0x44, 0xff, 0x2d, 0x05, 0x4b, 0xee,
				0xe0, 0x54, 0x9e, 0xae, 0x86, 0x45, 0xf3, 0xbd,
				0x0e, 0x42, 0xf2, 0xbf, 0x0f, 0x7f, 0xc6, 0x09,
				0x07, 0xdc, 0x22, 0x11, 0x77, 0xbe, 0x31, 0x27,
				0x5b, 0xa4, 0x13, 0x47, 0x07, 0x32, 0x9f, 0x1f,
				0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d, 0xf3, 0xe7,
				0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3, 0xe7, 0xcf,
				0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f,
				0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3f,
				0x53, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
				0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9,
				0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3,
				0xe7, 0xcf, 0x9f, 0x3e, 0x00, 0x46, 0x28, 0x26,
				0x20, 0x4a, 0x5a, 0xc0, 0x8a, 0xc5, 0xae, 0xa0,
				0x55, 0x78, 0x82, 0x7a, 0x38, 0x10, 0x09, 0xc9,
				0xb8, 0x0c, 0xfa, 0x5b, 0xc9, 0xd2, 0xec, 0x44,
				0x25, 0xf8, 0x20, 0xf2, 0xc8, 0x8a, 0xe9, 0x40,
				0x18, 0x06, 0xc6, 0x2b, 0xc8, 0xed, 0x8f, 0x33,
				0x09, 0x92, 0x28, 0x1e, 0xc4, 0x24, 0xd8, 0x33,
				0xa5, 0x00, 0xf5, 0xea, 0x18, 0xfa, 0x90, 0x97,
				0x97, 0xe8, 0x39, 0x6a, 0xcf, 0xf1, 0xdd, 0xff,
				0x9e, 0x8e, 0x04, 0x02, 0xae, 0x65, 0x87, 0x5c,
				0x4e, 0x72, 0xfd, 0x3c, 0x01, 0x86, 0xfe, 0x56,
				0x59, 0x74, 0x44, 0x3a, 0x40, 0x00, 0xec, 0xfc,
			},
			{
				0x0b, 0x77, 0x47, 0x11, 0x0c, 0x40, 0x2f, 0x84,
				0x2b, 0xc1, 0x07, 0x7a, 0xb0, 0xfa, 0xbb, 0xea,
				0xef, 0x9f, 0x57, 0x7c, 0xf9, 0xf3, 0xf7, 0xcf,
				0x9f, 0x3e, 0x32, 0xfe, 0xd5, 0xc1, 0x50, 0xde,
				0xc5, 0x1e, 0x73, 0xd2, 0x6c, 0xa6, 0x94, 0x46,
				0x4e, 0x92, 0x8c, 0x0f, 0xb9, 0xcf, 0xad, 0x07,
				0x54, 0x4a, 0x2e, 0xf3, 0x7d, 0x07, 0x2e, 0xa4,
				0x2f, 0xba, 0xbf, 0x39, 0xb5, 0xc9, 0x92, 0xa6,
				0xe1, 0xb4, 0x70, 0xc5, 0xc4, 0xb5, 0xe6, 0x5d,
				0x0f, 0xa8, 0x71, 0xa4, 0xcc, 0xc5, 0xbc, 0x75,
				0x67, 0x92, 0x52, 0x4f, 0x7e, 0x62, 0x1c, 0xa9,
				0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0, 0x44, 0x92,
				0x30, 0x3b, 0xf7, 0x61, 0xd6, 0x49, 0x96, 0x66,
				0x98, 0x28, 0x1a, 0x95, 0xa9, 0x42, 0xad, 0xb7,
				0x50, 0x90, 0xad, 0x1c, 0x34, 0x80, 0xe2, 0xef,
				0xcd, 0x41, 0x0b, 0xf0, 0x9d, 0x57, 0x62, 0x78,
				0xfd, 0xc6, 0xc2, 0x19, 0x9e, 0x26, 0x31, 0xca,
				0x1e, 0x75, 0xb1, 0x7a, 0x8e, 0xb5, 0x51, 0x3a,
				0xfe, 0xe4, 0xf1, 0x0b, 0x4f, 0x14, 0x90, 0xdb,
				0x9f, 0x44, 0x50, 0xbb, 0xef, 0x74, 0x00, 0x8c,
				0x1f, 0x97, 0xa1, 0xa2, 0xfa, 0x72, 0x16, 0x47,
				0xc6, 0xc0, 0xe5, 0xfe, 0x67, 0x03, 0x9c, 0xfe,
				0x62, 0x01, 0xa1, 0x00, 0x5d, 0xff, 0xa5, 0x03,
				0x59, 0xfa, 0xa8, 0x25, 0x5f, 0x6b, 0x83, 0x51,
				0xf2, 0xc0, 0x44, 0xff, 0x2d, 0x05, 0x4b, 0xee,
				0xe0, 0x54, 0x9e, 0xae, 0x86, 0x45, 0xf3, 0xbd,
				0x0e, 0x42, 0xf2, 0xbf, 0x0f, 0x7f, 0xc6, 0x09,
				0x07, 0xdc, 0x22, 0x11, 0x77, 0xbe, 0x31, 0x27,
				0x5b, 0xa4, 0x13, 0x47, 0x07, 0x32, 0x9f, 0x1f,
				0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d, 0xf3, 0xe7,
				0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3, 0xe7, 0xcf,
				0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f,
				0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3f,
				0x53, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
				0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9,
				0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3,
				0xe7, 0xcf, 0x9f, 0x3e, 0x00, 0x46, 0x28, 0x26,
				0x20, 0x4a, 0x5a, 0xc0, 0x8a, 0xc5, 0xae, 0xa0,
				0x55, 0x78, 0x82, 0x7a, 0x38, 0x10, 0x09, 0xc9,
				0xb8, 0x0c, 0xfa, 0x5b, 0xc9, 0xd2, 0xec, 0x44,
				0x25, 0xf8, 0x20, 0xf2, 0xc8, 0x8a, 0xe9, 0x40,
				0x18, 0x06, 0xc6, 0x2b, 0xc8, 0xed, 0x8f, 0x33,
				0x09, 0x92, 0x28, 0x1e, 0xc4, 0x24, 0xd8, 0x33,
				0xa5, 0x00, 0xf5, 0xea, 0x18, 0xfa, 0x90, 0x97,
				0x97, 0xe8, 0x39, 0x6a, 0xcf, 0xf1, 0xdd, 0xff,
				0x9e, 0x8e, 0x04, 0x02, 0xae, 0x65, 0x87, 0x5c,
				0x4e, 0x72, 0xfd, 0x3c, 0x01, 0x86, 0xfe, 0x56,
				0x59, 0x74, 0x44, 0x3a, 0x40, 0x00, 0xec, 0xfc,
			},
		},
		[]*rtp.Packet{
			{
				Header: rtp.Header{
					Version:        2,
					Marker:         true,
					PayloadType:    96,
					SequenceNumber: 17645,
					SSRC:           0x9dbb7812,
				},
				Payload: []byte{
					0x00, 0x02, 0x0b, 0x77, 0x47, 0x11, 0x0c, 0x40,
					0x2f, 0x84, 0x2b, 0xc1, 0x07, 0x7a, 0xb0, 0xfa,
					0xbb, 0xea, 0xef, 0x9f, 0x57, 0x7c, 0xf9, 0xf3,
					0xf7, 0xcf, 0x9f, 0x3e, 0x32, 0xfe, 0xd5, 0xc1,
					0x50, 0xde, 0xc5, 0x1e, 0x73, 0xd2, 0x6c, 0xa6,
					0x94, 0x46, 0x4e, 0x92, 0x8c, 0x0f, 0xb9, 0xcf,
					0xad, 0x07, 0x54, 0x4a, 0x2e, 0xf3, 0x7d, 0x07,
					0x2e, 0xa4, 0x2f, 0xba, 0xbf, 0x39, 0xb5, 0xc9,
					0x92, 0xa6, 0xe1, 0xb4, 0x70, 0xc5, 0xc4, 0xb5,
					0xe6, 0x5d, 0x0f, 0xa8, 0x71, 0xa4, 0xcc, 0xc5,
					0xbc, 0x75, 0x67, 0x92, 0x52, 0x4f, 0x7e, 0x62,
					0x1c, 0xa9, 0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0,
					0x44, 0x92, 0x30, 0x3b, 0xf7, 0x61, 0xd6, 0x49,
					0x96, 0x66, 0x98, 0x28, 0x1a, 0x95, 0xa9, 0x42,
					0xad, 0xb7, 0x50, 0x90, 0xad, 0x1c, 0x34, 0x80,
					0xe2, 0xef, 0xcd, 0x41, 0x0b, 0xf0, 0x9d, 0x57,
					0x62, 0x78, 0xfd, 0xc6, 0xc2, 0x19, 0x9e, 0x26,
					0x31, 0xca, 0x1e, 0x75, 0xb1, 0x7a, 0x8e, 0xb5,
					0x51, 0x3a, 0xfe, 0xe4, 0xf1, 0x0b, 0x4f, 0x14,
					0x90, 0xdb, 0x9f, 0x44, 0x50, 0xbb, 0xef, 0x74,
					0x00, 0x8c, 0x1f, 0x97, 0xa1, 0xa2, 0xfa, 0x72,
					0x16, 0x47, 0xc6, 0xc0, 0xe5, 0xfe, 0x67, 0x03,
					0x9c, 0xfe, 0x62, 0x01, 0xa1, 0x00, 0x5d, 0xff,
					0xa5, 0x03, 0x59, 0xfa, 0xa8, 0x25, 0x5f, 0x6b,
					0x83, 0x51, 0xf2, 0xc0, 0x44, 0xff, 0x2d, 0x05,
					0x4b, 0xee, 0xe0, 0x54, 0x9e, 0xae, 0x86, 0x45,
					0xf3, 0xbd, 0x0e, 0x42, 0xf2, 0xbf, 0x0f, 0x7f,
					0xc6, 0x09, 0x07, 0xdc, 0x22, 0x11, 0x77, 0xbe,
					0x31, 0x27, 0x5b, 0xa4, 0x13, 0x47, 0x07, 0x32,
					0x9f, 0x1f, 0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d,
					0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3,
					0xe7, 0xcf, 0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7,
					0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf,
					0x9f, 0x3f, 0x53, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f,
					0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e,
					0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
					0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x00, 0x46,
					0x28, 0x26, 0x20, 0x4a, 0x5a, 0xc0, 0x8a, 0xc5,
					0xae, 0xa0, 0x55, 0x78, 0x82, 0x7a, 0x38, 0x10,
					0x09, 0xc9, 0xb8, 0x0c, 0xfa, 0x5b, 0xc9, 0xd2,
					0xec, 0x44, 0x25, 0xf8, 0x20, 0xf2, 0xc8, 0x8a,
					0xe9, 0x40, 0x18, 0x06, 0xc6, 0x2b, 0xc8, 0xed,
					0x8f, 0x33, 0x09, 0x92, 0x28, 0x1e, 0xc4, 0x24,
					0xd8, 0x33, 0xa5, 0x00, 0xf5, 0xea, 0x18, 0xfa,
					0x90, 0x97, 0x97, 0xe8, 0x39, 0x6a, 0xcf, 0xf1,
					0xdd, 0xff, 0x9e, 0x8e, 0x04, 0x02, 0xae, 0x65,
					0x87, 0x5c, 0x4e, 0x72, 0xfd, 0x3c, 0x01, 0x86,
					0xfe, 0x56, 0x59, 0x74, 0x44, 0x3a, 0x40, 0x00,
					0xec, 0xfc, 0x0b, 0x77, 0x47, 0x11, 0x0c, 0x40,
					0x2f, 0x84, 0x2b, 0xc1, 0x07, 0x7a, 0xb0, 0xfa,
					0xbb, 0xea, 0xef, 0x9f, 0x57, 0x7c, 0xf9, 0xf3,
					0xf7, 0xcf, 0x9f, 0x3e, 0x32, 0xfe, 0xd5, 0xc1,
					0x50, 0xde, 0xc5, 0x1e, 0x73, 0xd2, 0x6c, 0xa6,
					0x94, 0x46, 0x4e, 0x92, 0x8c, 0x0f, 0xb9, 0xcf,
					0xad, 0x07, 0x54, 0x4a, 0x2e, 0xf3, 0x7d, 0x07,
					0x2e, 0xa4, 0x2f, 0xba, 0xbf, 0x39, 0xb5, 0xc9,
					0x92, 0xa6, 0xe1, 0xb4, 0x70, 0xc5, 0xc4, 0xb5,
					0xe6, 0x5d, 0x0f, 0xa8, 0x71, 0xa4, 0xcc, 0xc5,
					0xbc, 0x75, 0x67, 0x92, 0x52, 0x4f, 0x7e, 0x62,
					0x1c, 0xa9, 0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0,
					0x44, 0x92, 0x30, 0x3b, 0xf7, 0x61, 0xd6, 0x49,
					0x96, 0x66, 0x98, 0x28, 0x1a, 0x95, 0xa9, 0x42,
					0xad, 0xb7, 0x50, 0x90, 0xad, 0x1c, 0x34, 0x80,
					0xe2, 0xef, 0xcd, 0x41, 0x0b, 0xf0, 0x9d, 0x57,
					0x62, 0x78, 0xfd, 0xc6, 0xc2, 0x19, 0x9e, 0x26,
					0x31, 0xca, 0x1e, 0x75, 0xb1, 0x7a, 0x8e, 0xb5,
					0x51, 0x3a, 0xfe, 0xe4, 0xf1, 0x0b, 0x4f, 0x14,
					0x90, 0xdb, 0x9f, 0x44, 0x50, 0xbb, 0xef, 0x74,
					0x00, 0x8c, 0x1f, 0x97, 0xa1, 0xa2, 0xfa, 0x72,
					0x16, 0x47, 0xc6, 0xc0, 0xe5, 0xfe, 0x67, 0x03,
					0x9c, 0xfe, 0x62, 0x01, 0xa1, 0x00, 0x5d, 0xff,
					0xa5, 0x03, 0x59, 0xfa, 0xa8, 0x25, 0x5f, 0x6b,
					0x83, 0x51, 0xf2, 0xc0, 0x44, 0xff, 0x2d, 0x05,
					0x4b, 0xee, 0xe0, 0x54, 0x9e, 0xae, 0x86, 0x45,
					0xf3, 0xbd, 0x0e, 0x42, 0xf2, 0xbf, 0x0f, 0x7f,
					0xc6, 0x09, 0x07, 0xdc, 0x22, 0x11, 0x77, 0xbe,
					0x31, 0x27, 0x5b, 0xa4, 0x13, 0x47, 0x07, 0x32,
					0x9f, 0x1f, 0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d,
					0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3,
					0xe7, 0xcf, 0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7,
					0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf,
					0x9f, 0x3f, 0x53, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f,
					0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e,
					0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
					0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x00, 0x46,
					0x28, 0x26, 0x20, 0x4a, 0x5a, 0xc0, 0x8a, 0xc5,
					0xae, 0xa0, 0x55, 0x78, 0x82, 0x7a, 0x38, 0x10,
					0x09, 0xc9, 0xb8, 0x0c, 0xfa, 0x5b, 0xc9, 0xd2,
					0xec, 0x44, 0x25, 0xf8, 0x20, 0xf2, 0xc8, 0x8a,
					0xe9, 0x40, 0x18, 0x06, 0xc6, 0x2b, 0xc8, 0xed,
					0x8f, 0x33, 0x09, 0x92, 0x28, 0x1e, 0xc4, 0x24,
					0xd8, 0x33, 0xa5, 0x00, 0xf5, 0xea, 0x18, 0xfa,
					0x90, 0x97, 0x97, 0xe8, 0x39, 0x6a, 0xcf, 0xf1,
					0xdd, 0xff, 0x9e, 0x8e, 0x04, 0x02, 0xae, 0x65,
					0x87, 0x5c, 0x4e, 0x72, 0xfd, 0x3c, 0x01, 0x86,
					0xfe, 0x56, 0x59, 0x74, 0x44, 0x3a, 0x40, 0x00,
					0xec, 0xfc,
				},
			},
		},
	},
	{
		"fragmented",
		[][]byte{{
			0x0b, 0x77, 0x1a, 0x01, 0x1e, 0x40, 0xeb, 0xf8,
			0x40, 0x3e, 0xff, 0x99, 0xc5, 0x98, 0xb3, 0x16,
			0x62, 0xcc, 0x59, 0xff, 0xfa, 0xd7, 0xae, 0xf9,
			0x07, 0x7a, 0xb0, 0xfa, 0xbb, 0xea, 0xef, 0x9f,
			0x57, 0x7c, 0xf9, 0xf3, 0xf7, 0xc2, 0x0e, 0xf5,
			0x61, 0xf5, 0x77, 0xd5, 0xdf, 0x3e, 0xae, 0xf9,
			0xf3, 0xe7, 0xef, 0x84, 0x1d, 0xea, 0xc3, 0xea,
			0xef, 0xab, 0xbe, 0x7d, 0x5d, 0xf3, 0xe7, 0xcf,
			0xdf, 0x08, 0x3b, 0xd5, 0x87, 0xd5, 0xdf, 0x57,
			0x7c, 0xfa, 0xbb, 0xe7, 0xcf, 0x9f, 0xbe, 0x10,
			0x77, 0xab, 0x0f, 0xab, 0xbe, 0xae, 0xf9, 0xf5,
			0x77, 0xcf, 0x9f, 0x3f, 0x7c, 0x23, 0xc4, 0xf9,
			0x7f, 0x66, 0xa5, 0x4a, 0x95, 0x2a, 0x54, 0xa9,
			0x00, 0x54, 0x37, 0xb1, 0x4e, 0x73, 0xc9, 0xb3,
			0x69, 0x45, 0x93, 0xa2, 0x8c, 0x3e, 0xe7, 0xfa,
			0xd0, 0x75, 0x44, 0xa2, 0xef, 0x37, 0xd0, 0x72,
			0xea, 0x42, 0xfb, 0xad, 0xce, 0x6d, 0x72, 0x65,
			0x53, 0xe1, 0xa8, 0xe1, 0x8b, 0x89, 0x6b, 0xcc,
			0xb4, 0x3e, 0xa1, 0xc6, 0xa6, 0x8c, 0x57, 0x8d,
			0x5b, 0xca, 0x52, 0x4f, 0x7e, 0x62, 0x1c, 0xa9,
			0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0, 0x44, 0x92,
			0x30, 0x3b, 0xf7, 0x63, 0xb9, 0x2c, 0xb6, 0x73,
			0x0a, 0x0d, 0x45, 0xa2, 0x8a, 0xbd, 0xc5, 0x02,
			0x1b, 0x4e, 0x24, 0x81, 0xcb, 0xce, 0x74, 0x11,
			0x7c, 0x2e, 0xb7, 0x65, 0x24, 0x6e, 0x6c, 0x46,
			0x67, 0x89, 0x8c, 0x72, 0x87, 0x9d, 0x70, 0xca,
			0x9d, 0x55, 0x0a, 0xaf, 0xdd, 0x38, 0x8b, 0x5e,
			0x52, 0x87, 0xbb, 0xe9, 0x10, 0x5b, 0xfe, 0xd0,
			0x04, 0xc3, 0xf1, 0xe8, 0x68, 0xbe, 0x9c, 0x85,
			0x91, 0xf1, 0xb0, 0x39, 0x7f, 0x99, 0x82, 0x9d,
			0xfd, 0x88, 0x0a, 0x00, 0x17, 0x7f, 0xa6, 0x05,
			0x67, 0xea, 0xa0, 0x95, 0x7d, 0xae, 0x0d, 0x47,
			0xcb, 0x01, 0x13, 0xfc, 0xb4, 0x15, 0x2f, 0xbb,
			0x81, 0x52, 0x7a, 0xba, 0x19, 0x17, 0xab, 0xc1,
			0xd0, 0xf9, 0xc0, 0x1d, 0xfe, 0x21, 0x21, 0xfe,
			0x22, 0x21, 0xed, 0xe3, 0x25, 0xdd, 0xe4, 0x26,
			0xa3, 0x2a, 0x32, 0xa3, 0x34, 0xe0, 0x26, 0x72,
			0x58, 0x40, 0x95, 0x0d, 0xec, 0x53, 0x9c, 0xf2,
			0x6c, 0xda, 0x51, 0x64, 0xe8, 0xa3, 0x0f, 0xb9,
			0xfe, 0xb4, 0x1d, 0x51, 0x28, 0xbb, 0xcd, 0xf4,
			0x1c, 0xba, 0x90, 0xbe, 0xeb, 0x73, 0x9b, 0x5c,
			0x99, 0x54, 0xf8, 0x6a, 0x38, 0x62, 0xe2, 0x5a,
			0xf3, 0x2d, 0x0f, 0xa8, 0x71, 0xa9, 0xa3, 0x15,
			0xe3, 0x56, 0xf2, 0x94, 0x93, 0xdf, 0x98, 0x87,
			0x2a, 0x76, 0x6d, 0x46, 0x5a, 0xb5, 0xec, 0x11,
			0x24, 0x8c, 0x0e, 0xfd, 0xd8, 0xee, 0x4b, 0x2d,
			0x9c, 0xc2, 0x83, 0x51, 0x68, 0xa2, 0xaf, 0x71,
			0x40, 0x86, 0xd3, 0x89, 0x20, 0x72, 0xf3, 0x9d,
			0x04, 0x5f, 0x0b, 0xad, 0xd9, 0x49, 0x1b, 0x9b,
			0x11, 0x99, 0xe2, 0x63, 0x1c, 0xa1, 0xe7, 0x5c,
			0x32, 0xa7, 0x55, 0x42, 0xab, 0xf7, 0x4e, 0x22,
			0xd7, 0x94, 0xa1, 0xee, 0xfa, 0x44, 0x16, 0xff,
			0xb4, 0x01, 0x30, 0xfc, 0x7a, 0x1a, 0x2f, 0xa7,
			0x21, 0x64, 0x7c, 0x6c, 0x0e, 0x5f, 0xe6, 0x60,
			0xa7, 0x7f, 0x62, 0x02, 0x80, 0x05, 0xdf, 0xe9,
			0x81, 0x59, 0xfa, 0xa8, 0x25, 0x5f, 0x6b, 0x83,
			0x51, 0xf2, 0xc0, 0x44, 0xff, 0x2d, 0x05, 0x4b,
			0xee, 0xe0, 0x54, 0x9e, 0xae, 0x86, 0x45, 0xea,
			0xf0, 0x74, 0x3e, 0x70, 0x07, 0x7f, 0x88, 0x48,
			0x7f, 0x88, 0x88, 0x7b, 0x78, 0xc9, 0x77, 0x79,
			0x09, 0x54, 0x37, 0xb1, 0x4e, 0x73, 0xc9, 0xb3,
			0x69, 0x45, 0x93, 0xa2, 0x8c, 0x3e, 0xe7, 0xfa,
			0xd0, 0x75, 0x44, 0xa2, 0xef, 0x37, 0xd0, 0x72,
			0xea, 0x42, 0xfb, 0xad, 0xce, 0x6d, 0x72, 0x65,
			0x53, 0xe1, 0xa8, 0xe1, 0x8b, 0x89, 0x6b, 0xcc,
			0xb4, 0x3e, 0xa1, 0xc6, 0xa6, 0x8c, 0x57, 0x8d,
			0x5b, 0xca, 0x52, 0x4f, 0x7e, 0x62, 0x1c, 0xa9,
			0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0, 0x44, 0x92,
			0x30, 0x3b, 0xf7, 0x63, 0xb9, 0x2c, 0xb6, 0x73,
			0x0a, 0x0d, 0x45, 0xa2, 0x8a, 0xbd, 0xc5, 0x02,
			0x1b, 0x4e, 0x24, 0x81, 0xcb, 0xce, 0x74, 0x11,
			0x7c, 0x2e, 0xb7, 0x65, 0x24, 0x6e, 0x6c, 0x46,
			0x67, 0x89, 0x8c, 0x72, 0x87, 0x9d, 0x70, 0xca,
			0x9d, 0x55, 0x0a, 0xaf, 0xdd, 0x38, 0x8b, 0x5e,
			0x52, 0x87, 0xbb, 0xe9, 0x10, 0x5b, 0xfe, 0xd0,
			0x04, 0xc3, 0xf1, 0xe8, 0x68, 0xbe, 0x9c, 0x85,
			0x91, 0xf1, 0xb0, 0x39, 0x7f, 0x99, 0x82, 0x9d,
			0xfd, 0x88, 0x0a, 0x00, 0x17, 0x7f, 0xa6, 0x05,
			0x67, 0xea, 0xa0, 0x95, 0x7d, 0xae, 0x0d, 0x47,
			0xcb, 0x01, 0x13, 0xfc, 0xb4, 0x15, 0x2f, 0xbb,
			0x81, 0x52, 0x7a, 0xba, 0x19, 0x17, 0xab, 0xc1,
			0xd0, 0xf9, 0xc0, 0x1d, 0xfe, 0x21, 0x21, 0xfe,
			0x22, 0x21, 0xed, 0xe3, 0x25, 0xdd, 0xe4, 0x25,
			0x50, 0xde, 0xc5, 0x39, 0xcf, 0x26, 0xcd, 0xa5,
			0x16, 0x4e, 0x8a, 0x30, 0xfb, 0x9f, 0xeb, 0x41,
			0xd5, 0x12, 0x8b, 0xbc, 0xdf, 0x41, 0xcb, 0xa9,
			0x0b, 0xee, 0xb7, 0x39, 0xb5, 0xc9, 0x95, 0x4f,
			0x86, 0xa3, 0x86, 0x2e, 0x25, 0xaf, 0x32, 0xd0,
			0xfa, 0x87, 0x1a, 0x9a, 0x31, 0x5e, 0x35, 0x6f,
			0x29, 0x49, 0x3d, 0xf9, 0x88, 0x72, 0xa7, 0x66,
			0xd4, 0x65, 0xab, 0x5e, 0xc1, 0x12, 0x48, 0xc0,
			0xef, 0xdd, 0x8e, 0xe4, 0xb2, 0xd9, 0xcc, 0x28,
			0x35, 0x16, 0x8a, 0x2a, 0xf7, 0x14, 0x08, 0x6d,
			0x38, 0x92, 0x07, 0x2f, 0x39, 0xd0, 0x45, 0xf0,
			0xba, 0xdd, 0x94, 0x91, 0xb9, 0xb1, 0x19, 0x9e,
			0x26, 0x31, 0xca, 0x1e, 0x75, 0xc3, 0x2a, 0x75,
			0x54, 0x2a, 0xbf, 0x74, 0xe2, 0x2d, 0x79, 0x4a,
			0x1e, 0xef, 0xa4, 0x41, 0x6f, 0xfb, 0x40, 0x13,
			0x0f, 0xc7, 0xa1, 0xa2, 0xfa, 0x72, 0x16, 0x47,
			0xc6, 0xc0, 0xe5, 0xfe, 0x66, 0x0a, 0x77, 0xf6,
			0x20, 0x28, 0x00, 0x5d, 0xfe, 0x98, 0x15, 0x9f,
			0xaa, 0x82, 0x55, 0xf6, 0xb8, 0x35, 0x1f, 0x2c,
			0x04, 0x4f, 0xf2, 0xd0, 0x54, 0xbe, 0xee, 0x05,
			0x49, 0xea, 0xe8, 0x64, 0x5e, 0xaf, 0x07, 0x43,
			0xe7, 0x00, 0x77, 0xf8, 0x84, 0x87, 0xf8, 0x88,
			0x87, 0xb7, 0x8c, 0x97, 0x77, 0x90, 0x95, 0x43,
			0x7b, 0x14, 0xe7, 0x3c, 0x9b, 0x36, 0x94, 0x59,
			0x3a, 0x28, 0xc3, 0xee, 0x7f, 0xad, 0x07, 0x54,
			0x4a, 0x2e, 0xf3, 0x7d, 0x07, 0x2e, 0xa4, 0x2f,
			0xba, 0xdc, 0xe6, 0xd7, 0x26, 0x55, 0x3e, 0x1a,
			0x8e, 0x18, 0xb8, 0x96, 0xbc, 0xcb, 0x43, 0xea,
			0x1c, 0x6a, 0x68, 0xc5, 0x78, 0xd5, 0xbc, 0xa5,
			0x24, 0xf7, 0xe6, 0x21, 0xca, 0x9d, 0x9b, 0x51,
			0x96, 0xad, 0x7b, 0x04, 0x49, 0x23, 0x03, 0xbf,
			0x76, 0x3b, 0x92, 0xcb, 0x67, 0x30, 0xa0, 0xd4,
			0x5a, 0x28, 0xab, 0xdc, 0x50, 0x21, 0xb4, 0xe2,
			0x48, 0x1c, 0xbc, 0xe7, 0x41, 0x17, 0xc2, 0xeb,
			0x76, 0x52, 0x46, 0xe6, 0xc4, 0x66, 0x78, 0x98,
			0xc7, 0x28, 0x79, 0xd7, 0x0c, 0xa9, 0xd5, 0x50,
			0xaa, 0xfd, 0xd3, 0x88, 0xb5, 0xe5, 0x28, 0x7b,
			0xbe, 0x91, 0x05, 0xbf, 0xed, 0x00, 0x4c, 0x3f,
			0x1e, 0x86, 0x8b, 0xe9, 0xc8, 0x59, 0x1f, 0x1b,
			0x03, 0x97, 0xf9, 0x98, 0x29, 0xdf, 0xd8, 0x80,
			0xa0, 0x01, 0x77, 0xfa, 0x60, 0x56, 0x7e, 0xaa,
			0x09, 0x57, 0xda, 0xe0, 0xd4, 0x7c, 0xb0, 0x11,
			0x3f, 0xcb, 0x41, 0x52, 0xfb, 0xb8, 0x15, 0x27,
			0xab, 0xa1, 0x91, 0x7a, 0xbc, 0x1d, 0x0f, 0x9c,
			0x01, 0xdf, 0xe2, 0x12, 0x1f, 0xe2, 0x22, 0x1e,
			0xde, 0x32, 0x5d, 0xde, 0x42, 0x55, 0x15, 0x8a,
			0x9c, 0xf4, 0x9b, 0x33, 0x4a, 0x0c, 0x9d, 0x51,
			0x80, 0x7c, 0x04, 0xaa, 0xa9, 0xf3, 0xe7, 0xcf,
			0x9f, 0x3e, 0x73, 0x29, 0xf1, 0xfc, 0xbb, 0x0d,
			0xf3, 0xe7, 0xd0, 0xdf, 0x3e, 0x7c, 0xf9, 0xf3,
			0xea, 0xef, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe8,
			0x55, 0xdf, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf,
			0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xf5, 0x35, 0xdf,
			0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e,
			0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x1c,
			0xca, 0x7c, 0x7f, 0x2e, 0xc3, 0x7c, 0xf9, 0xf4,
			0x37, 0xcf, 0x9f, 0x3e, 0x7c, 0xfa, 0xbb, 0xe7,
			0xcf, 0x9f, 0x3e, 0x7c, 0xfa, 0x15, 0x77, 0xcf,
			0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f,
			0x3e, 0x7c, 0xfd, 0x4d, 0x77, 0xcf, 0x9f, 0x3e,
			0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
			0xf9, 0xf3, 0xe7, 0xcf, 0x87, 0x32, 0x9f, 0x1f,
			0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d, 0xf3, 0xe7,
			0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3, 0xe7, 0xcf,
			0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f,
			0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3f,
			0x53, 0x5d, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
			0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9,
			0xf3, 0xe1, 0xcc, 0xa7, 0xc7, 0xf2, 0xec, 0x37,
			0xcf, 0x9f, 0x43, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf,
			0xab, 0xbe, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0xa1,
			0x57, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e,
			0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0xd4, 0xd7, 0x7c,
			0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9,
			0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf8, 0x73,
			0x29, 0xf1, 0xfc, 0xbb, 0x0d, 0xf3, 0xe7, 0xd0,
			0xdf, 0x3e, 0x7c, 0xf9, 0xf3, 0xea, 0xef, 0x9f,
			0x3e, 0x7c, 0xf9, 0xf3, 0xe8, 0x55, 0xdf, 0x3e,
			0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c,
			0xf9, 0xf3, 0xf5, 0x35, 0xdf, 0x3e, 0x7c, 0xf9,
			0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3,
			0xe7, 0xcf, 0x9f, 0x3e, 0x00, 0x23, 0x28, 0x26,
			0x20, 0x4a, 0x5a, 0xc2, 0x2b, 0x5a, 0xea, 0x05,
			0x57, 0x88, 0x27, 0x47, 0x80, 0x11, 0x94, 0x13,
			0x10, 0x25, 0x2d, 0x61, 0x15, 0xad, 0x75, 0x02,
			0xab, 0xc4, 0x13, 0xa0, 0x8c, 0xa0, 0x98, 0x81,
			0x29, 0x6b, 0x08, 0xad, 0x6b, 0xa8, 0x15, 0x5e,
			0x20, 0x9d, 0x1e, 0x08, 0xca, 0x09, 0x88, 0x12,
			0x96, 0xb0, 0x8a, 0xd6, 0xba, 0x81, 0x55, 0xe2,
			0x09, 0xd0, 0x02, 0x32, 0x82, 0x62, 0x04, 0xa5,
			0xac, 0x22, 0xb5, 0xae, 0xa0, 0x55, 0x78, 0x82,
			0x74, 0x6e, 0x04, 0x85, 0x08, 0x81, 0x29, 0x6b,
			0x08, 0xad, 0x6b, 0xa8, 0x15, 0x03, 0xe0, 0x00,
			0x00, 0x09, 0xcb, 0x70, 0x19, 0xf4, 0xb7, 0x94,
			0x97, 0x60, 0x84, 0xbf, 0x04, 0x1e, 0x59, 0x02,
			0xbc, 0x6c, 0xe5, 0xb8, 0x0c, 0xfa, 0x5b, 0xca,
			0x4b, 0xb0, 0x42, 0x5f, 0x82, 0x0f, 0x2c, 0x81,
			0x67, 0x2d, 0xc0, 0x67, 0xd2, 0xde, 0x52, 0x5d,
			0x82, 0x12, 0xfc, 0x10, 0x79, 0x64, 0x0a, 0xf2,
			0x72, 0xdc, 0x06, 0x7d, 0x2d, 0xe5, 0x25, 0xd8,
			0x21, 0x2f, 0xc1, 0x07, 0x96, 0x40, 0xac, 0x9c,
			0xb7, 0x01, 0x9f, 0x4b, 0x79, 0x49, 0x76, 0x08,
			0x4b, 0xf0, 0x41, 0xe5, 0x90, 0x2b, 0x7f, 0x3b,
			0xee, 0x27, 0xd2, 0xde, 0x52, 0x5d, 0x82, 0x12,
			0xfc, 0x10, 0x1f, 0x01, 0x80, 0x0a, 0xae, 0xf9,
			0xf0, 0x18, 0x0d, 0x8c, 0x57, 0x91, 0xdb, 0x1d,
			0x98, 0x52, 0x45, 0x03, 0xd8, 0x84, 0x9b, 0x0c,
			0xf1, 0xac, 0x06, 0xc6, 0x2b, 0xc8, 0xed, 0x8e,
			0xcc, 0x29, 0x22, 0x81, 0xec, 0x42, 0x4d, 0x86,
			0x60, 0x36, 0x31, 0x5e, 0x47, 0x6c, 0x76, 0x61,
			0x49, 0x14, 0x0f, 0x62, 0x12, 0x6c, 0x33, 0xc6,
			0x03, 0x63, 0x15, 0xe4, 0x76, 0xc7, 0x66, 0x14,
			0x91, 0x40, 0xf6, 0x21, 0x26, 0xc3, 0x31, 0x80,
			0xd8, 0xc5, 0x79, 0x1d, 0xb1, 0xd9, 0x85, 0x24,
			0x50, 0x3d, 0x88, 0x49, 0xb0, 0xcd, 0xc3, 0x07,
			0xb2, 0x5e, 0x47, 0x6c, 0x76, 0x61, 0x49, 0x14,
			0x0f, 0x60, 0x7c, 0x04, 0x00, 0x29, 0xf3, 0xe7,
			0xcf, 0x9f, 0x3e, 0x07, 0xae, 0xa1, 0x8f, 0xa9,
			0x09, 0x79, 0xfa, 0x0f, 0x6a, 0xcf, 0xf1, 0xdd,
			0xff, 0xad, 0x1e, 0x03, 0xd7, 0x50, 0xc7, 0xd4,
			0x84, 0xbc, 0xfd, 0x07, 0xb5, 0x67, 0xf8, 0xee,
			0xff, 0xd6, 0x9e, 0xba, 0x86, 0x3e, 0xa4, 0x25,
			0xe7, 0xe8, 0x3d, 0xab, 0x3f, 0xc7, 0x77, 0xfe,
			0xb4, 0x79, 0xeb, 0xa8, 0x63, 0xea, 0x42, 0x5e,
			0x7e, 0x83, 0xda, 0xb3, 0xfc, 0x77, 0x7f, 0xeb,
			0x40, 0x7a, 0xea, 0x18, 0xfa, 0x90, 0x97, 0x9f,
			0xa0, 0xf6, 0xac, 0xff, 0x1d, 0xdf, 0xfa, 0xd1,
			0xbf, 0xf6, 0x14, 0x5e, 0xa4, 0x25, 0xe7, 0xe8,
			0x3d, 0xab, 0x3f, 0xc6, 0x0f, 0x80, 0x00, 0x00,
			0x2a, 0xec, 0xb0, 0xeb, 0x89, 0xce, 0x62, 0x9e,
			0x03, 0x0d, 0xfc, 0xac, 0xb2, 0xe8, 0x90, 0xf1,
			0xb5, 0x76, 0x58, 0x75, 0xc4, 0xe7, 0x31, 0x4f,
			0x01, 0x86, 0xfe, 0x56, 0x59, 0x74, 0x48, 0xab,
			0xb2, 0xc3, 0xae, 0x27, 0x39, 0x8a, 0x78, 0x0c,
			0x37, 0xf2, 0xb2, 0xcb, 0xa2, 0x43, 0xca, 0xbb,
			0x2c, 0x3a, 0xe2, 0x73, 0x98, 0xa7, 0x80, 0xc3,
			0x7f, 0x2b, 0x2c, 0xba, 0x24, 0x32, 0xae, 0xcb,
			0x0e, 0xb8, 0x9c, 0xe6, 0x29, 0xe0, 0x30, 0xdf,
			0xca, 0xcb, 0x2e, 0x89, 0x0d, 0xfd, 0x67, 0x96,
			0xae, 0x27, 0x39, 0x8a, 0x78, 0x0c, 0x37, 0xf2,
			0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe4, 0xa2,
		}},
		[]*rtp.Packet{
			{
				Header: rtp.Header{
					Version:        2,
					Marker:         false,
					PayloadType:    96,
					SequenceNumber: 17645,
					SSRC:           0x9dbb7812,
				},
				Payload: []byte{
					0x01, 0x02, 0x0b, 0x77, 0x1a, 0x01, 0x1e, 0x40,
					0xeb, 0xf8, 0x40, 0x3e, 0xff, 0x99, 0xc5, 0x98,
					0xb3, 0x16, 0x62, 0xcc, 0x59, 0xff, 0xfa, 0xd7,
					0xae, 0xf9, 0x07, 0x7a, 0xb0, 0xfa, 0xbb, 0xea,
					0xef, 0x9f, 0x57, 0x7c, 0xf9, 0xf3, 0xf7, 0xc2,
					0x0e, 0xf5, 0x61, 0xf5, 0x77, 0xd5, 0xdf, 0x3e,
					0xae, 0xf9, 0xf3, 0xe7, 0xef, 0x84, 0x1d, 0xea,
					0xc3, 0xea, 0xef, 0xab, 0xbe, 0x7d, 0x5d, 0xf3,
					0xe7, 0xcf, 0xdf, 0x08, 0x3b, 0xd5, 0x87, 0xd5,
					0xdf, 0x57, 0x7c, 0xfa, 0xbb, 0xe7, 0xcf, 0x9f,
					0xbe, 0x10, 0x77, 0xab, 0x0f, 0xab, 0xbe, 0xae,
					0xf9, 0xf5, 0x77, 0xcf, 0x9f, 0x3f, 0x7c, 0x23,
					0xc4, 0xf9, 0x7f, 0x66, 0xa5, 0x4a, 0x95, 0x2a,
					0x54, 0xa9, 0x00, 0x54, 0x37, 0xb1, 0x4e, 0x73,
					0xc9, 0xb3, 0x69, 0x45, 0x93, 0xa2, 0x8c, 0x3e,
					0xe7, 0xfa, 0xd0, 0x75, 0x44, 0xa2, 0xef, 0x37,
					0xd0, 0x72, 0xea, 0x42, 0xfb, 0xad, 0xce, 0x6d,
					0x72, 0x65, 0x53, 0xe1, 0xa8, 0xe1, 0x8b, 0x89,
					0x6b, 0xcc, 0xb4, 0x3e, 0xa1, 0xc6, 0xa6, 0x8c,
					0x57, 0x8d, 0x5b, 0xca, 0x52, 0x4f, 0x7e, 0x62,
					0x1c, 0xa9, 0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0,
					0x44, 0x92, 0x30, 0x3b, 0xf7, 0x63, 0xb9, 0x2c,
					0xb6, 0x73, 0x0a, 0x0d, 0x45, 0xa2, 0x8a, 0xbd,
					0xc5, 0x02, 0x1b, 0x4e, 0x24, 0x81, 0xcb, 0xce,
					0x74, 0x11, 0x7c, 0x2e, 0xb7, 0x65, 0x24, 0x6e,
					0x6c, 0x46, 0x67, 0x89, 0x8c, 0x72, 0x87, 0x9d,
					0x70, 0xca, 0x9d, 0x55, 0x0a, 0xaf, 0xdd, 0x38,
					0x8b, 0x5e, 0x52, 0x87, 0xbb, 0xe9, 0x10, 0x5b,
					0xfe, 0xd0, 0x04, 0xc3, 0xf1, 0xe8, 0x68, 0xbe,
					0x9c, 0x85, 0x91, 0xf1, 0xb0, 0x39, 0x7f, 0x99,
					0x82, 0x9d, 0xfd, 0x88, 0x0a, 0x00, 0x17, 0x7f,
					0xa6, 0x05, 0x67, 0xea, 0xa0, 0x95, 0x7d, 0xae,
					0x0d, 0x47, 0xcb, 0x01, 0x13, 0xfc, 0xb4, 0x15,
					0x2f, 0xbb, 0x81, 0x52, 0x7a, 0xba, 0x19, 0x17,
					0xab, 0xc1, 0xd0, 0xf9, 0xc0, 0x1d, 0xfe, 0x21,
					0x21, 0xfe, 0x22, 0x21, 0xed, 0xe3, 0x25, 0xdd,
					0xe4, 0x26, 0xa3, 0x2a, 0x32, 0xa3, 0x34, 0xe0,
					0x26, 0x72, 0x58, 0x40, 0x95, 0x0d, 0xec, 0x53,
					0x9c, 0xf2, 0x6c, 0xda, 0x51, 0x64, 0xe8, 0xa3,
					0x0f, 0xb9, 0xfe, 0xb4, 0x1d, 0x51, 0x28, 0xbb,
					0xcd, 0xf4, 0x1c, 0xba, 0x90, 0xbe, 0xeb, 0x73,
					0x9b, 0x5c, 0x99, 0x54, 0xf8, 0x6a, 0x38, 0x62,
					0xe2, 0x5a, 0xf3, 0x2d, 0x0f, 0xa8, 0x71, 0xa9,
					0xa3, 0x15, 0xe3, 0x56, 0xf2, 0x94, 0x93, 0xdf,
					0x98, 0x87, 0x2a, 0x76, 0x6d, 0x46, 0x5a, 0xb5,
					0xec, 0x11, 0x24, 0x8c, 0x0e, 0xfd, 0xd8, 0xee,
					0x4b, 0x2d, 0x9c, 0xc2, 0x83, 0x51, 0x68, 0xa2,
					0xaf, 0x71, 0x40, 0x86, 0xd3, 0x89, 0x20, 0x72,
					0xf3, 0x9d, 0x04, 0x5f, 0x0b, 0xad, 0xd9, 0x49,
					0x1b, 0x9b, 0x11, 0x99, 0xe2, 0x63, 0x1c, 0xa1,
					0xe7, 0x5c, 0x32, 0xa7, 0x55, 0x42, 0xab, 0xf7,
					0x4e, 0x22, 0xd7, 0x94, 0xa1, 0xee, 0xfa, 0x44,
					0x16, 0xff, 0xb4, 0x01, 0x30, 0xfc, 0x7a, 0x1a,
					0x2f, 0xa7, 0x21, 0x64, 0x7c, 0x6c, 0x0e, 0x5f,
					0xe6, 0x60, 0xa7, 0x7f, 0x62, 0x02, 0x80, 0x05,
					0xdf, 0xe9, 0x81, 0x59, 0xfa, 0xa8, 0x25, 0x5f,
					0x6b, 0x83, 0x51, 0xf2, 0xc0, 0x44, 0xff, 0x2d,
					0x05, 0x4b, 0xee, 0xe0, 0x54, 0x9e, 0xae, 0x86,
					0x45, 0xea, 0xf0, 0x74, 0x3e, 0x70, 0x07, 0x7f,
					0x88, 0x48, 0x7f, 0x88, 0x88, 0x7b, 0x78, 0xc9,
					0x77, 0x79, 0x09, 0x54, 0x37, 0xb1, 0x4e, 0x73,
					0xc9, 0xb3, 0x69, 0x45, 0x93, 0xa2, 0x8c, 0x3e,
					0xe7, 0xfa, 0xd0, 0x75, 0x44, 0xa2, 0xef, 0x37,
					0xd0, 0x72, 0xea, 0x42, 0xfb, 0xad, 0xce, 0x6d,
					0x72, 0x65, 0x53, 0xe1, 0xa8, 0xe1, 0x8b, 0x89,
					0x6b, 0xcc, 0xb4, 0x3e, 0xa1, 0xc6, 0xa6, 0x8c,
					0x57, 0x8d, 0x5b, 0xca, 0x52, 0x4f, 0x7e, 0x62,
					0x1c, 0xa9, 0xd9, 0xb5, 0x19, 0x6a, 0xd7, 0xb0,
					0x44, 0x92, 0x30, 0x3b, 0xf7, 0x63, 0xb9, 0x2c,
					0xb6, 0x73, 0x0a, 0x0d, 0x45, 0xa2, 0x8a, 0xbd,
					0xc5, 0x02, 0x1b, 0x4e, 0x24, 0x81, 0xcb, 0xce,
					0x74, 0x11, 0x7c, 0x2e, 0xb7, 0x65, 0x24, 0x6e,
					0x6c, 0x46, 0x67, 0x89, 0x8c, 0x72, 0x87, 0x9d,
					0x70, 0xca, 0x9d, 0x55, 0x0a, 0xaf, 0xdd, 0x38,
					0x8b, 0x5e, 0x52, 0x87, 0xbb, 0xe9, 0x10, 0x5b,
					0xfe, 0xd0, 0x04, 0xc3, 0xf1, 0xe8, 0x68, 0xbe,
					0x9c, 0x85, 0x91, 0xf1, 0xb0, 0x39, 0x7f, 0x99,
					0x82, 0x9d, 0xfd, 0x88, 0x0a, 0x00, 0x17, 0x7f,
					0xa6, 0x05, 0x67, 0xea, 0xa0, 0x95, 0x7d, 0xae,
					0x0d, 0x47, 0xcb, 0x01, 0x13, 0xfc, 0xb4, 0x15,
					0x2f, 0xbb, 0x81, 0x52, 0x7a, 0xba, 0x19, 0x17,
					0xab, 0xc1, 0xd0, 0xf9, 0xc0, 0x1d, 0xfe, 0x21,
					0x21, 0xfe, 0x22, 0x21, 0xed, 0xe3, 0x25, 0xdd,
					0xe4, 0x25, 0x50, 0xde, 0xc5, 0x39, 0xcf, 0x26,
					0xcd, 0xa5, 0x16, 0x4e, 0x8a, 0x30, 0xfb, 0x9f,
					0xeb, 0x41, 0xd5, 0x12, 0x8b, 0xbc, 0xdf, 0x41,
					0xcb, 0xa9, 0x0b, 0xee, 0xb7, 0x39, 0xb5, 0xc9,
					0x95, 0x4f, 0x86, 0xa3, 0x86, 0x2e, 0x25, 0xaf,
					0x32, 0xd0, 0xfa, 0x87, 0x1a, 0x9a, 0x31, 0x5e,
					0x35, 0x6f, 0x29, 0x49, 0x3d, 0xf9, 0x88, 0x72,
					0xa7, 0x66, 0xd4, 0x65, 0xab, 0x5e, 0xc1, 0x12,
					0x48, 0xc0, 0xef, 0xdd, 0x8e, 0xe4, 0xb2, 0xd9,
					0xcc, 0x28, 0x35, 0x16, 0x8a, 0x2a, 0xf7, 0x14,
					0x08, 0x6d, 0x38, 0x92, 0x07, 0x2f, 0x39, 0xd0,
					0x45, 0xf0, 0xba, 0xdd, 0x94, 0x91, 0xb9, 0xb1,
					0x19, 0x9e, 0x26, 0x31, 0xca, 0x1e, 0x75, 0xc3,
					0x2a, 0x75, 0x54, 0x2a, 0xbf, 0x74, 0xe2, 0x2d,
					0x79, 0x4a, 0x1e, 0xef, 0xa4, 0x41, 0x6f, 0xfb,
					0x40, 0x13, 0x0f, 0xc7, 0xa1, 0xa2, 0xfa, 0x72,
					0x16, 0x47, 0xc6, 0xc0, 0xe5, 0xfe, 0x66, 0x0a,
					0x77, 0xf6, 0x20, 0x28, 0x00, 0x5d, 0xfe, 0x98,
					0x15, 0x9f, 0xaa, 0x82, 0x55, 0xf6, 0xb8, 0x35,
					0x1f, 0x2c, 0x04, 0x4f, 0xf2, 0xd0, 0x54, 0xbe,
					0xee, 0x05, 0x49, 0xea, 0xe8, 0x64, 0x5e, 0xaf,
					0x07, 0x43, 0xe7, 0x00, 0x77, 0xf8, 0x84, 0x87,
					0xf8, 0x88, 0x87, 0xb7, 0x8c, 0x97, 0x77, 0x90,
					0x95, 0x43, 0x7b, 0x14, 0xe7, 0x3c, 0x9b, 0x36,
					0x94, 0x59, 0x3a, 0x28, 0xc3, 0xee, 0x7f, 0xad,
					0x07, 0x54, 0x4a, 0x2e, 0xf3, 0x7d, 0x07, 0x2e,
					0xa4, 0x2f, 0xba, 0xdc, 0xe6, 0xd7, 0x26, 0x55,
					0x3e, 0x1a, 0x8e, 0x18, 0xb8, 0x96, 0xbc, 0xcb,
					0x43, 0xea, 0x1c, 0x6a, 0x68, 0xc5, 0x78, 0xd5,
					0xbc, 0xa5, 0x24, 0xf7, 0xe6, 0x21, 0xca, 0x9d,
					0x9b, 0x51, 0x96, 0xad, 0x7b, 0x04, 0x49, 0x23,
					0x03, 0xbf, 0x76, 0x3b, 0x92, 0xcb, 0x67, 0x30,
					0xa0, 0xd4, 0x5a, 0x28, 0xab, 0xdc, 0x50, 0x21,
					0xb4, 0xe2, 0x48, 0x1c, 0xbc, 0xe7, 0x41, 0x17,
					0xc2, 0xeb, 0x76, 0x52, 0x46, 0xe6, 0xc4, 0x66,
					0x78, 0x98, 0xc7, 0x28, 0x79, 0xd7, 0x0c, 0xa9,
					0xd5, 0x50, 0xaa, 0xfd, 0xd3, 0x88, 0xb5, 0xe5,
					0x28, 0x7b, 0xbe, 0x91, 0x05, 0xbf, 0xed, 0x00,
					0x4c, 0x3f, 0x1e, 0x86, 0x8b, 0xe9, 0xc8, 0x59,
					0x1f, 0x1b, 0x03, 0x97, 0xf9, 0x98, 0x29, 0xdf,
					0xd8, 0x80, 0xa0, 0x01, 0x77, 0xfa, 0x60, 0x56,
					0x7e, 0xaa, 0x09, 0x57, 0xda, 0xe0, 0xd4, 0x7c,
					0xb0, 0x11, 0x3f, 0xcb, 0x41, 0x52, 0xfb, 0xb8,
					0x15, 0x27, 0xab, 0xa1, 0x91, 0x7a, 0xbc, 0x1d,
					0x0f, 0x9c, 0x01, 0xdf, 0xe2, 0x12, 0x1f, 0xe2,
					0x22, 0x1e, 0xde, 0x32, 0x5d, 0xde, 0x42, 0x55,
					0x15, 0x8a, 0x9c, 0xf4, 0x9b, 0x33, 0x4a, 0x0c,
					0x9d, 0x51, 0x80, 0x7c, 0x04, 0xaa, 0xa9, 0xf3,
					0xe7, 0xcf, 0x9f, 0x3e, 0x73, 0x29, 0xf1, 0xfc,
					0xbb, 0x0d, 0xf3, 0xe7, 0xd0, 0xdf, 0x3e, 0x7c,
					0xf9, 0xf3, 0xea, 0xef, 0x9f, 0x3e, 0x7c, 0xf9,
					0xf3, 0xe8, 0x55, 0xdf, 0x3e, 0x7c, 0xf9, 0xf3,
					0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xf5,
					0x35, 0xdf, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf,
					0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f,
					0x3e, 0x1c, 0xca, 0x7c, 0x7f, 0x2e, 0xc3, 0x7c,
					0xf9, 0xf4, 0x37, 0xcf, 0x9f, 0x3e, 0x7c, 0xfa,
					0xbb, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xfa, 0x15,
					0x77, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7,
					0xcf, 0x9f, 0x3e, 0x7c, 0xfd, 0x4d, 0x77, 0xcf,
					0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x9f,
					0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0x87, 0x32,
					0x9f, 0x1f, 0xcb, 0xb0, 0xdf, 0x3e, 0x7d, 0x0d,
					0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0xae, 0xf9, 0xf3,
					0xe7, 0xcf, 0x9f, 0x3e, 0x85, 0x5d, 0xf3, 0xe7,
					0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf,
					0x9f, 0x3f, 0x53, 0x5d, 0xf3, 0xe7,
				},
			},
			{
				Header: rtp.Header{
					Version:        2,
					Marker:         true,
					PayloadType:    96,
					SequenceNumber: 17646,
					SSRC:           0x9dbb7812,
				},
				Payload: []byte{
					0x03, 0x02, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3,
					0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe1,
					0xcc, 0xa7, 0xc7, 0xf2, 0xec, 0x37, 0xcf, 0x9f,
					0x43, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0xab, 0xbe,
					0x7c, 0xf9, 0xf3, 0xe7, 0xcf, 0xa1, 0x57, 0x7c,
					0xf9, 0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9,
					0xf3, 0xe7, 0xcf, 0xd4, 0xd7, 0x7c, 0xf9, 0xf3,
					0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7,
					0xcf, 0x9f, 0x3e, 0x7c, 0xf8, 0x73, 0x29, 0xf1,
					0xfc, 0xbb, 0x0d, 0xf3, 0xe7, 0xd0, 0xdf, 0x3e,
					0x7c, 0xf9, 0xf3, 0xea, 0xef, 0x9f, 0x3e, 0x7c,
					0xf9, 0xf3, 0xe8, 0x55, 0xdf, 0x3e, 0x7c, 0xf9,
					0xf3, 0xe7, 0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3,
					0xf5, 0x35, 0xdf, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7,
					0xcf, 0x9f, 0x3e, 0x7c, 0xf9, 0xf3, 0xe7, 0xcf,
					0x9f, 0x3e, 0x00, 0x23, 0x28, 0x26, 0x20, 0x4a,
					0x5a, 0xc2, 0x2b, 0x5a, 0xea, 0x05, 0x57, 0x88,
					0x27, 0x47, 0x80, 0x11, 0x94, 0x13, 0x10, 0x25,
					0x2d, 0x61, 0x15, 0xad, 0x75, 0x02, 0xab, 0xc4,
					0x13, 0xa0, 0x8c, 0xa0, 0x98, 0x81, 0x29, 0x6b,
					0x08, 0xad, 0x6b, 0xa8, 0x15, 0x5e, 0x20, 0x9d,
					0x1e, 0x08, 0xca, 0x09, 0x88, 0x12, 0x96, 0xb0,
					0x8a, 0xd6, 0xba, 0x81, 0x55, 0xe2, 0x09, 0xd0,
					0x02, 0x32, 0x82, 0x62, 0x04, 0xa5, 0xac, 0x22,
					0xb5, 0xae, 0xa0, 0x55, 0x78, 0x82, 0x74, 0x6e,
					0x04, 0x85, 0x08, 0x81, 0x29, 0x6b, 0x08, 0xad,
					0x6b, 0xa8, 0x15, 0x03, 0xe0, 0x00, 0x00, 0x09,
					0xcb, 0x70, 0x19, 0xf4, 0xb7, 0x94, 0x97, 0x60,
					0x84, 0xbf, 0x04, 0x1e, 0x59, 0x02, 0xbc, 0x6c,
					0xe5, 0xb8, 0x0c, 0xfa, 0x5b, 0xca, 0x4b, 0xb0,
					0x42, 0x5f, 0x82, 0x0f, 0x2c, 0x81, 0x67, 0x2d,
					0xc0, 0x67, 0xd2, 0xde, 0x52, 0x5d, 0x82, 0x12,
					0xfc, 0x10, 0x79, 0x64, 0x0a, 0xf2, 0x72, 0xdc,
					0x06, 0x7d, 0x2d, 0xe5, 0x25, 0xd8, 0x21, 0x2f,
					0xc1, 0x07, 0x96, 0x40, 0xac, 0x9c, 0xb7, 0x01,
					0x9f, 0x4b, 0x79, 0x49, 0x76, 0x08, 0x4b, 0xf0,
					0x41, 0xe5, 0x90, 0x2b, 0x7f, 0x3b, 0xee, 0x27,
					0xd2, 0xde, 0x52, 0x5d, 0x82, 0x12, 0xfc, 0x10,
					0x1f, 0x01, 0x80, 0x0a, 0xae, 0xf9, 0xf0, 0x18,
					0x0d, 0x8c, 0x57, 0x91, 0xdb, 0x1d, 0x98, 0x52,
					0x45, 0x03, 0xd8, 0x84, 0x9b, 0x0c, 0xf1, 0xac,
					0x06, 0xc6, 0x2b, 0xc8, 0xed, 0x8e, 0xcc, 0x29,
					0x22, 0x81, 0xec, 0x42, 0x4d, 0x86, 0x60, 0x36,
					0x31, 0x5e, 0x47, 0x6c, 0x76, 0x61, 0x49, 0x14,
					0x0f, 0x62, 0x12, 0x6c, 0x33, 0xc6, 0x03, 0x63,
					0x15, 0xe4, 0x76, 0xc7, 0x66, 0x14, 0x91, 0x40,
					0xf6, 0x21, 0x26, 0xc3, 0x31, 0x80, 0xd8, 0xc5,
					0x79, 0x1d, 0xb1, 0xd9, 0x85, 0x24, 0x50, 0x3d,
					0x88, 0x49, 0xb0, 0xcd, 0xc3, 0x07, 0xb2, 0x5e,
					0x47, 0x6c, 0x76, 0x61, 0x49, 0x14, 0x0f, 0x60,
					0x7c, 0x04, 0x00, 0x29, 0xf3, 0xe7, 0xcf, 0x9f,
					0x3e, 0x07, 0xae, 0xa1, 0x8f, 0xa9, 0x09, 0x79,
					0xfa, 0x0f, 0x6a, 0xcf, 0xf1, 0xdd, 0xff, 0xad,
					0x1e, 0x03, 0xd7, 0x50, 0xc7, 0xd4, 0x84, 0xbc,
					0xfd, 0x07, 0xb5, 0x67, 0xf8, 0xee, 0xff, 0xd6,
					0x9e, 0xba, 0x86, 0x3e, 0xa4, 0x25, 0xe7, 0xe8,
					0x3d, 0xab, 0x3f, 0xc7, 0x77, 0xfe, 0xb4, 0x79,
					0xeb, 0xa8, 0x63, 0xea, 0x42, 0x5e, 0x7e, 0x83,
					0xda, 0xb3, 0xfc, 0x77, 0x7f, 0xeb, 0x40, 0x7a,
					0xea, 0x18, 0xfa, 0x90, 0x97, 0x9f, 0xa0, 0xf6,
					0xac, 0xff, 0x1d, 0xdf, 0xfa, 0xd1, 0xbf, 0xf6,
					0x14, 0x5e, 0xa4, 0x25, 0xe7, 0xe8, 0x3d, 0xab,
					0x3f, 0xc6, 0x0f, 0x80, 0x00, 0x00, 0x2a, 0xec,
					0xb0, 0xeb, 0x89, 0xce, 0x62, 0x9e, 0x03, 0x0d,
					0xfc, 0xac, 0xb2, 0xe8, 0x90, 0xf1, 0xb5, 0x76,
					0x58, 0x75, 0xc4, 0xe7, 0x31, 0x4f, 0x01, 0x86,
					0xfe, 0x56, 0x59, 0x74, 0x48, 0xab, 0xb2, 0xc3,
					0xae, 0x27, 0x39, 0x8a, 0x78, 0x0c, 0x37, 0xf2,
					0xb2, 0xcb, 0xa2, 0x43, 0xca, 0xbb, 0x2c, 0x3a,
					0xe2, 0x73, 0x98, 0xa7, 0x80, 0xc3, 0x7f, 0x2b,
					0x2c, 0xba, 0x24, 0x32, 0xae, 0xcb, 0x0e, 0xb8,
					0x9c, 0xe6, 0x29, 0xe0, 0x30, 0xdf, 0xca, 0xcb,
					0x2e, 0x89, 0x0d, 0xfd, 0x67, 0x96, 0xae, 0x27,
					0x39, 0x8a, 0x78, 0x0c, 0x37, 0xf2, 0xb0, 0x00,
					0x00, 0x00, 0x00, 0x00, 0xe4, 0xa2,
				},
			},
		},
	},
}

func TestEncode(t *testing.T) {
	for _, ca := range cases {
		t.Run(ca.name, func(t *testing.T) {
			e := &Encoder{
				PayloadType:           96,
				SSRC:                  uint32Ptr(0x9dbb7812),
				InitialSequenceNumber: uint16Ptr(0x44ed),
				PayloadMaxSize:        1200,
			}
			err := e.Init()
			require.NoError(t, err)

			pkts, err := e.Encode(ca.frames)
			require.NoError(t, err)
			require.Equal(t, ca.pkts, pkts)
		})
	}
}

func TestEncodeRandomInitialState(t *testing.T) {
	e := &Encoder{
		PayloadType: 96,
	}
	err := e.Init()
	require.NoError(t, err)
	require.NotEqual(t, nil, e.SSRC)
	require.NotEqual(t, nil, e.InitialSequenceNumber)
}
