name: test

on:
  push:
    branches: [ main, v4 ]
  pull_request:
    branches: [ main, v4 ]

jobs:
  test:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        go: ["1.23", "1.24"]

    steps:
    - uses: actions/checkout@v4

    - uses: actions/setup-go@v3
      with:
        go-version: ${{ matrix.go }}

    - run: sudo apt update && sudo apt install -y libavcodec-dev libswscale-dev

    - run: make test-nodocker

    - if: matrix.go == '1.24'
      uses: codecov/codecov-action@v3
      with:
        token: ${{ secrets.CODECOV_TOKEN }}

  test_e2e:
    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v4

    - uses: actions/setup-go@v3
      with:
        go-version: "1.24"

    - run: make test-e2e-nodocker

  test_32:
    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v4

    - run: make test-32
