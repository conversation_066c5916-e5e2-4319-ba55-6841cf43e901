name: lint

on:
  push:
    branches: [ main, v4 ]
  pull_request:
    branches: [ main, v4 ]

jobs:
  golangci-lint:
    runs-on: ubuntu-22.04

    env:
      CGO_ENABLED: '0'

    steps:
    - uses: actions/checkout@v4

    - uses: actions/setup-go@v3
      with:
        go-version: "1.24"

    - uses: golangci/golangci-lint-action@v8
      with:
        version: v2.3.0

  go-mod-tidy:
    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v4

    - uses: actions/setup-go@v3
      with:
        go-version: "1.24"

    - run: |
        go mod tidy
        git diff --exit-code
