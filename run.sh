#!/bin/bash

# Configuration
SESSION_NAME="fermax-onvif"
VENV_PATH="venv"
SCRIPT_PATH="main.py"

# Check if screen session already exists
if screen -list | grep -q "\.${SESSION_NAME}[[:space:]]"; then
    echo "Screen session '${SESSION_NAME}' already exists. Attaching to it..."
    screen -r "${SESSION_NAME}"
else
    echo "Creating new screen session '${SESSION_NAME}'..."
    # Create new screen session, activate venv, and run main.py
    screen -dmS "${SESSION_NAME}" bash -c "
        echo 'Activating virtual environment...'
        source ${VENV_PATH}/bin/activate
        echo 'Starting Fermax DuoxMe ONVIF handler...'
        python ${SCRIPT_PATH}
        echo 'Script ended. Press any key to exit.'
        read
    "
    echo "Screen session '${SESSION_NAME}' created and script started."
    echo "To attach to the session, run: screen -r ${SESSION_NAME}"
    echo "To detach from the session, press Ctrl+A then D"
fi