# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Python project that creates a virtual ONVIF camera device that bridges Fermax DuoxMe doorbell systems with ONVIF-compatible clients. The application connects to the Fermax cloud service via WebRTC to receive live video feeds and exposes them as RTSP streams through a local ONVIF device interface.

## Key Dependencies

- **Python 3.x** with asyncio for asynchronous operations
- **MediaMTX** binary (must be present in root directory) - media streaming server
- **FFmpeg** with VAAPI support - video processing and encoding
- **aiohttp** - HTTP server for REST API and ONVIF endpoints
- **aiortc** - WebRTC client for connecting to Fermax signaling server
- **socketio** - Real-time communication with Fermax cloud service
- **PIL/Pillow** - Image processing for dummy frames
- **numpy** - Frame data manipulation

## Running the Application

```bash
# Create and activate virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Ensure MediaMTX binary is in root directory

# Run the application directly
python main.py

# Or use the run script for screen session management
./run.sh
```

The `run.sh` script provides convenient session management:
- Creates a detached screen session named `fermax-onvif`
- Automatically activates the virtual environment
- Runs the application in the background
- Allows you to attach/detach from the session
- If session exists, attaches to it directly

**Screen session commands:**
- `./run.sh` - Start new session or attach to existing
- `screen -r fermax-onvif` - Attach to running session
- `Ctrl+A then D` - Detach from session (keeps app running)
- `screen -list` - List all screen sessions

## API Usage Examples

### Video-Only Session (existing functionality)
```bash
curl -X POST http://localhost:8080/start \
  -H "Content-Type: application/json" \
  -d '{
    "ROOM_ID": "your_room_id",
    "FERMAX_OAUTH_TOKEN": "your_oauth_token", 
    "APP_TOKEN": "your_app_token",
    "SERVER_URL": "https://calldivertsignserver01-pro-west-europe-blue.fermax.io:443"
  }'
```

### Audio Reception Session (video + audio reception)
```bash
curl -X POST http://localhost:8080/start_twoway \
  -H "Content-Type: application/json" \
  -d '{
    "ROOM_ID": "your_room_id",
    "FERMAX_OAUTH_TOKEN": "your_oauth_token",
    "APP_TOKEN": "your_app_token",
    "SERVER_URL": "https://calldivertsignserver01-pro-west-europe-blue.fermax.io:443"
  }'
```

**Note**: The `SERVER_URL` parameter is optional and defaults to `calldivertsignserver01-pro-west-europe-blue.fermax.io:443`. Different regions may use different servers (e.g., `calldivertsignserver02`, `calldivertsignserver03`, etc.).

### Stop Session
```bash
curl -X POST http://localhost:8080/stop
```

## Key Configuration

Located in `config.py`:
- **ONVIF_PORT**: 8080 (ONVIF device service port)
- **RTSP_PORT**: 8554 (RTSP stream port)
- **MEDIAMTX_URL**: rtsp://127.0.0.1:8554/doorbell
- **VIDEO_WIDTH/HEIGHT**: 854x480
- **DUMMY_FPS**: 24

## Architecture Overview

### Core Components

1. **ONVIF Device Service** (`onvif_service.py`):
   - Implements ONVIF protocol handlers for device discovery and capabilities
   - Handles WS-Discovery multicast protocol for device discovery
   - Processes SOAP requests for device information, media profiles, and event subscriptions
   - Manages event notifications for doorbell press events

2. **WebRTC Client** (`webrtc_client.py`):
   - Connects to Fermax signaling server via Socket.IO
   - Handles WebRTC negotiation for video-only streams
   - Processes live video frames from doorbell camera
   - Manages connection state and error handling

3. **Audio Reception WebRTC Client** (`webrtc_twoway_client.py`):
   - Extends WebRTC functionality for audio reception
   - Implements "pickup" event for call answering (required for audio reception)
   - Handles multiple transport connections (video receive, audio receive)
   - Processes both video and audio tracks

4. **Media Pipeline** (`media_pipeline.py`):
   - Streams dummy image when no live feed is available
   - Switches to live video frames when WebRTC connection is active
   - Feeds processed frames to FFmpeg for RTSP encoding
   - Uses asyncio.Queue for frame buffering (60 frame capacity)

5. **FFmpeg Integration** (`main.py`):
   - Processes raw video frames through stdin pipe
   - Adds dummy audio track for ONVIF compliance
   - Outputs H.264/AAC encoded RTSP stream to MediaMTX
   - Uses VAAPI hardware acceleration when available

6. **MediaMTX Server** (`main.py`):
   - Handles RTSP stream serving to ONVIF clients
   - Configured via `mediamtx.yml` with doorbell path
   - Manages stream publishing and client connections

### HTTP API Endpoints

Located in `http_handlers.py`:
- **POST /start**: Initiates video-only doorbell streaming session with auth tokens
- **POST /start_twoway**: Initiates audio reception doorbell session with auth tokens (includes video + audio reception)
- **POST /stop**: Terminates active streaming session
- **POST /press_doorbell**: Triggers doorbell press event notification

Located in `onvif_service.py`:
- **POST /onvif/device_service**: Main ONVIF SOAP endpoint
- **GET /onvif/snapshot**: Returns dummy image snapshot

### Event System

The application implements ONVIF event notifications for doorbell presses:
- Events are queued in `g_event_queue` (config.py)
- Long-polling mechanism for PullMessages requests (onvif_service.py)
- Automatic ON/OFF sequence with configurable duration (http_handlers.py)

## Network Captures

The `network_captures/` directory contains sample WebRTC signaling traffic:
- `video_watch_only/`: Video-only connection flow
- `pick_up_call_two_way_audio/`: Audio reception connection flow

## Key Global State

Located in `config.py`:
- `current_stream_task`: Active WebRTC streaming task
- `ffmpeg_process`/`mediamtx_process`: External process handles
- `live_frame_queue`: Frame buffer for live video
- `is_live`: Event flag for live/dummy stream switching
- `g_event_queue`: ONVIF event notification queue

## Project Structure

After refactoring, the code is organized into the following modules:

- **`main.py`**: Entry point and main application loop
- **`config.py`**: Configuration constants and global state variables
- **`onvif_service.py`**: ONVIF device service and WS-Discovery implementation
- **`onvif_templates.py`**: XML templates for ONVIF SOAP responses
- **`webrtc_client.py`**: WebRTC client for Fermax signaling server (video-only)
- **`webrtc_twoway_client.py`**: WebRTC client for audio reception functionality
- **`media_pipeline.py`**: Stream generation and frame processing
- **`http_handlers.py`**: HTTP API endpoint handlers
- **`utils.py`**: Utility functions for networking and process management

## Audio Reception Implementation Details

### **Three-Phase Protocol Implementation**
The audio reception implementation (`webrtc_twoway_client.py`) follows a precise three-phase protocol based on network capture analysis:

**Phase 1: Video-Only Setup (identical to working video_watch_only)**
- Establish Socket.IO connection to Fermax signaling server
- Emit `join_call` with retry logic (up to 10 attempts with 1s delays for room availability)
- Consume video transport: `transport_consume` for VIDEO producer
- Setup video-only SDP with H.264 codec
- Connect video transport: `transport_connect` for recvTransportVideo

**Phase 2: Pickup Event (required for audio reception)**
- Emit `pickup` event to signal call answering
- This enables the server to start sending audio from the doorbell

**Phase 3: Dedicated Audio Transport Setup (CRITICAL for audio reception)**
- Consume audio transport: `transport_consume` for AUDIO producer on recvTransportAudio
- Create new combined video+audio SDP with both media types
- Renegotiate WebRTC connection with updated SDP
- Connect dedicated audio transport: `transport_connect` for recvTransportAudio

### **Critical Network Log Compliance**
The implementation follows the exact Socket.IO event sequence from `network_captures/pick_up_call_two_way_audio/`:

**Required Event Sequence:**
1. `join_call` → server returns multiple transports (recvTransportVideo, recvTransportAudio)
2. `transport_consume` for VIDEO on recvTransportVideo
3. `transport_connect` for recvTransportVideo (role: "client")
4. `pickup` event to signal call answering
5. **`transport_consume` for AUDIO on recvTransportAudio** (Phase 3)
6. **`transport_connect` for recvTransportAudio** (Phase 3)

**Critical Parameters:**
- Audio codec: `audio/PCMA` at 8000Hz, 1 channel, payload type 8
- Separate transport IDs for video and audio reception

### **Key Architectural Insights**
- **Separate Audio Transport**: The server provides a dedicated `recvTransportAudio` distinct from video transport
- **Post-Pickup Audio Setup**: Audio transport consumption only occurs AFTER pickup event
- **WebRTC Renegotiation**: SDP must be updated to include audio media section for proper reception
- **Audio Reception**: Dedicated transport enables incoming audio from doorbell

### **Differences from Video-Only**
- **Video-Only** (`/start`): Single video transport, no pickup event, no audio handling
- **Audio Reception** (`/start_twoway`): Multiple transports, pickup event, dedicated audio transport setup
- **Session Management**: Both use same `config.current_stream_task`, single `/stop` endpoint works for both

### **Network Capture Reference**
When modifying WebRTC functionality, always reference the network captures:
- `network_captures/video_watch_only/` - Video-only flow (single transport)
- `network_captures/pick_up_call_two_way_audio/` - Audio reception flow (multi-transport)

**WARNING**: Any deviation from the exact network log sequence will cause the Fermax server to reject the connection. The three-phase protocol is mandatory for proper audio reception.

## Development Notes

- The application runs as a single async main loop managing multiple concurrent tasks
- Critical processes (MediaMTX, FFmpeg, stream generator) are monitored; if any exits, the entire application shuts down
- Hardware acceleration via VAAPI is configured but may need adjustment for different systems
- Authentication tokens (ROOM_ID, FERMAX_OAUTH_TOKEN, APP_TOKEN) are provided via /start and /start_twoway endpoints
- **Never modify the working video-only implementation** - it's tested and functional
- **Always verify network log compliance** when making WebRTC changes

## Technical Memories

### MediaSoup Audio Transport Architecture (CRITICAL)
The MediaSoup server uses separate transports for video and audio, not combined SDP negotiation. This was the root cause of the audio track creation issue.

**Key Learnings from Network Capture Analysis:**
- **Pickup Consumer vs Transport Consumer**: These are two completely different entities:
  - **Pickup Consumer** (`mid=0`): Created by pickup event, used only for pickup event response
  - **Transport Consumer** (`mid=1`): Created by `transport_consume` on `recvTransportAudio`, creates actual audio tracks
- **SDP Configuration**: Must use transport consumer parameters, not pickup consumer parameters:
  - Correct: `mid=1` from transport consumer with `a=sendrecv`
  - Incorrect: `mid=0` from pickup consumer with `a=recvonly`
- **Separate Peer Connections**: Audio and video must use completely separate RTCPeerConnection objects
- **Transport Sequence**: Video transport connects first, then audio transport connects after pickup event

**Common Mistakes to Avoid:**
- Using pickup consumer parameters for audio SDP (causes mid mismatch)
- Using `a=recvonly` instead of `a=sendrecv` in audio SDP
- Trying to use single peer connection for both video and audio
- Assuming pickup consumer creates audio tracks (it doesn't)

**Debug Process:**
1. Network capture analysis is essential - never assume WebRTC behavior
2. Check that audio transceiver has correct `mid` value from transport consumer
3. Verify SDP direction matches network captures (`sendrecv` not `recvonly`)
4. Confirm separate peer connections are used for video and audio transports
5. Validate transport consumer parameters are used for SDP, not pickup consumer

**Success Indicators:**
- Audio transceiver created with `mid=1` and `direction=sendrecv`
- Audio PC connection state reaches "connected"
- `receiver.track` is populated after transport connection
- VLC receives audio stream in addition to video

### Audio Reception Implementation Status (July 2025)
**CURRENT STATUS: ✅ WORKING - Audio reception functionality implemented and functional**
- Video stream: ✅ Working (H.264 1280x720)
- Audio reception: ✅ Working (PCMA 8000Hz from doorbell)
- Audio transmission: ✅ Working (from static file)

## **Current Implementation**

### **What's Working**
1. **Video Reception:** Full H.264 video stream from doorbell camera
2. **Audio Reception:** PCMA 8000Hz audio stream from doorbell microphone
3. **Three-Phase Protocol:** Proper WebRTC connection establishment
4. **Pickup Event:** Required for enabling audio reception from doorbell
5. **Dual Transport:** Separate video and audio transports working correctly

### **What's Been Removed**
1. **Audio Transmission Code:** All code related to sending audio to the doorbell
2. **Audio Track Classes:** DummyAudioTrack, GeneratedAudioTrack, InputAudioTrack, etc.
3. **Audio Input Endpoint:** HTTP /audio_input endpoint removed
4. **External Timing Loops:** Audio transmission monitoring and frame generation
5. **Send Transport:** No longer setting up audio transmission transport

### **Clean Architecture**
The implementation now focuses solely on what works:
- **Video-only mode** (`/start`): Single video transport
- **Audio reception mode** (`/start_twoway`): Video + audio reception via pickup event
- **No audio transmission complexity:** Simplified, maintainable codebase

### **Key Benefits**
- **Reliability:** Only working functionality is preserved
- **Maintainability:** Removed complex, non-working audio transmission code
- **Performance:** No unnecessary audio processing or monitoring
- **Clarity:** Clear separation between working and non-working features

## **Implementation Files**
- **Main Implementation:** `webrtc_twoway_client.py` - Audio reception WebRTC client
- **HTTP Handlers:** `http_handlers.py` - API endpoints for starting/stopping sessions
- **Configuration:** `config.py` - Global configuration and state management
- **Three-Phase Protocol:** Video setup → Pickup event → Audio transport (working correctly)
- **Network Capture Compliance:** Follows exact MediaSoup event sequence

---

# Claude Analysis - Two-Way Audio Transmission Investigation (July 2025)

## Current Status
- ✅ **Video Reception**: Working perfectly - can see doorbell video feed
- ✅ **Audio Reception**: Working perfectly - can hear doorbell audio
- ❌ **Audio Transmission**: NOT working - doorbell cannot hear us (mobile app works fine)

## Investigation Timeline & Key Discoveries

### Phase 1: Initial Diagnosis (Incorrect)
**Initial Theory**: Server authorization/OAuth token issues
- Investigated ROLE_USER vs ROLE_DEVICE token permissions
- Tested with mobile app's user token - same behavior
- **Result**: OAuth tokens are NOT the issue

### Phase 2: SDP Direction Discovery (CRITICAL FIX #1)
**Root Cause Found**: Manual SDP generation with wrong direction
- **Issue**: Line 775 in webrtc_twoway_client.py had `a=sendonly`
- **Problem**: This tells aiortc that server will only SEND, not RECEIVE from us
- **Fix**: Changed to `a=recvonly` (server receives from us)
- **Result**: FileAudioTrack.recv() finally started being called ✅

### Phase 3: Audio Format Issue (CRITICAL FIX #2)
**Root Cause Found**: Wrong AudioFrame format for G.711 encoder
- **Issue**: FileAudioTrack created frames with `format='flt'` (float32)
- **Problem**: G.711 PCMA encoder requires `format='s16'` (16-bit signed int)
- **Error**: `AssertionError` in `/aiortc/codecs/g711.py` line 53
- **Fix**: Changed FileAudioTrack to use `format='s16'`
- **Result**: No more encoder errors, frames processed successfully ✅

### Phase 4: Two-Way Audio Transmission Working ✅

**What's Working**:
- ✅ Video reception from doorbell (H.264 1280x720)
- ✅ Audio reception from doorbell (PCMA 8000Hz)
- ✅ **Audio transmission to doorbell (PCMA 8000Hz from static files)** 🎉
- ✅ Complete WebRTC three-phase protocol implementation
- ✅ FileAudioTrack pipeline with proper audio content

### Phase 5: Two-Way Audio from ONVIF

- Audio from ONVIF needs to be routed to the doorbell.

## Technical Fixes Applied

### Fix 1: SDP Direction Correction
```python
# webrtc_twoway_client.py line 775
# BEFORE (wrong):
f"a=sendonly\r\n"  # Server sends audio (opposite of client's recvonly)

# AFTER (correct):
f"a=recvonly\r\n"  # Server receives audio from client (we are sending)
```

### Fix 2: Audio Format Correction
```python
# audio_tracks.py lines 124-132
# BEFORE (wrong):
frame_data = frame_samples.astype(np.float32) / 32768.0
frame = AudioFrame.from_ndarray(
    frame_data.reshape(1, -1),
    format='flt',  # Wrong format
    layout='mono'
)

# AFTER (correct):
frame_data = frame_samples  # Keep as np.int16
frame = AudioFrame.from_ndarray(
    frame_data.reshape(1, -1),
    format='s16',  # Required by G.711 encoder
    layout='mono'
)
```

## Audio Content Requirements Discovery

### Successful Implementation:
- Complete WebRTC transmission pipeline working
- Audio successfully received and processed by doorbell

## Key Lessons Learned
1. **Manual SDP generation is error-prone** - wrong direction caused major issues
2. **Audio format compatibility is critical** - G.711 encoder has strict requirements
3. **Diagnostic messages can be misleading** - "No outbound-rtp stats" while packets are flowing
4. **WebRTC debugging requires multiple layers** - application, transport, and media levels

## Critical Debugging Insights
- **The mobile app works perfectly** - this proves the server and doorbell hardware are functional
- **Our WebRTC transmission appears successful** - packets sent, no errors, valid audio data
- **The issue must be in our implementation** - subtle difference from mobile app behavior
- **Focus on protocol-level differences** - not hardware or server-side issues

---

# ONVIF Backchannel Support Implementation (July 2025)

## Overview
Implemented ONVIF backchannel support to enable audio transmission from ONVIF clients to the doorbell. This establishes the foundation for bidirectional audio communication through the ONVIF protocol.

## Implementation Status
✅ **Phase 1: ONVIF Service Extensions** - Complete
✅ **Phase 2: Audio Input Infrastructure with Logging** - Complete
🔄 **Phase 3: WebRTC Integration** - Pending (not implemented)

## Architecture Overview

### ONVIF Backchannel Flow
```
ONVIF Client → POST /audio_input → Audio Queue → [Future: WebRTC → Doorbell]
```

### Current Implementation (Phases 1-2)
1. **ONVIF Discovery**: Client discovers TwoWayAudio capability
2. **Audio Configuration**: Client configures audio output via ONVIF endpoints
3. **Audio Reception**: Client sends audio data to `/audio_input` endpoint
4. **Logging & Verification**: Audio is logged, analyzed, and saved to files
5. **Queue Management**: Audio buffered in `onvif_audio_queue` for future processing

## Phase 1: ONVIF Service Extensions

### New ONVIF Endpoints
- **`GetAudioOutputConfigurations`**: Lists available audio output configurations
- **`GetAudioOutputConfigurationOptions`**: Returns available audio output settings and ranges
- **`SetAudioOutputConfiguration`**: Configures audio output parameters

### Device Capabilities Updates
- **TwoWayAudio capability**: Added `<tt:TwoWayAudio>true</tt:TwoWayAudio>` to streaming capabilities
- **Audio output configuration**: Added `AudioOutputConfiguration` to media profiles
- **Backchannel advertisement**: Clients can now discover bidirectional audio support

### Files Modified
- `onvif_service.py`: Added endpoint handlers for audio output configuration
- `onvif_templates.py`: Added XML response templates for audio output endpoints
- `onvif_templates.py`: Updated capabilities and profiles to include backchannel support

## Phase 2: Audio Input Infrastructure

### HTTP Audio Input Endpoint
**Endpoint**: `POST /audio_input`
**Purpose**: Receive audio data from ONVIF clients for backchannel transmission

**Supported Formats**:
- `audio/pcm` - Raw PCM audio data
- `audio/g711` - G.711 encoded audio (μ-law/A-law)
- `audio/wav` - WAV format audio

**Features**:
- Content-Type validation and format detection
- Audio size limits (1MB max per chunk)
- Comprehensive error handling and validation

### Audio Logging and Verification System

**Comprehensive Logging**:
- Audio format detection and characteristics analysis
- Sample count estimation for PCM data
- Amplitude analysis for silence detection
- Reception statistics and total data tracking

**File Output System**:
- **Raw audio files**: `audio_logs/audio_received_YYYYMMDD_HHMMSS_<format>.raw`
- **WAV files**: `audio_logs/audio_received_YYYYMMDD_HHMMSS_pcm.wav` (for PCM data)
- **Automatic rotation**: Keeps last 10 audio files, deletes older ones
- **Directory management**: Auto-creates `audio_logs/` directory

**Audio Analysis Features**:
- Format-specific analysis (PCM sample counting, amplitude checking)
- Basic silence detection on audio samples
- Content-Type header parsing for format determination
- Audio statistics tracking across sessions

### Configuration Variables

Added to `config.py`:
```python
# ONVIF audio input configuration
onvif_audio_queue = asyncio.Queue(maxsize=200)  # Buffer for incoming audio
onvif_audio_logging_enabled = True              # Enable detailed logging
onvif_audio_save_to_file = True                 # Save audio to files
onvif_audio_file_rotation_count = 10            # Keep last N files
```

### Audio Queue Management
- **Buffer size**: 200 audio chunks maximum
- **Non-blocking**: Uses `put_nowait()` to prevent blocking on full queue
- **Data structure**: Queued items include audio data, format, timestamp, and metadata
- **Future integration**: Ready for WebRTC audio track consumption

## Testing and Verification

### ONVIF Capability Testing
```bash
# Test TwoWayAudio capability discovery
curl -X POST http://localhost:8080/onvif/device_service \
  -H "Content-Type: application/soap+xml" \
  -d '<s:Body><tds:GetCapabilities/></s:Body>'

# Test audio output configuration
curl -X POST http://localhost:8080/onvif/device_service \
  -H "Content-Type: application/soap+xml" \
  -d '<s:Body><trt:GetAudioOutputConfigurations/></s:Body>'
```

### Audio Input Testing
```bash
# Test PCM audio reception
curl -X POST http://localhost:8080/audio_input \
  -H "Content-Type: audio/pcm" \
  --data-binary @test_audio.raw

# Test G.711 audio reception
curl -X POST http://localhost:8080/audio_input \
  -H "Content-Type: audio/g711" \
  --data-binary @test_audio.g711
```

### Log Analysis
Monitor logs for audio reception verification:
```
[AUDIO INPUT] Received 1600 bytes of pcm audio data
[AUDIO INPUT] PCM analysis: ~800 samples (assuming 16-bit)
[AUDIO INPUT] Sample amplitude check: max=15420 (of 32767)
[AUDIO INPUT] Saved raw audio to audio_logs/audio_received_20250127_142530_pcm.raw
[AUDIO INPUT] Saved WAV audio to audio_logs/audio_received_20250127_142530_pcm.wav
```

## Future Integration (Phase 3)

### Planned WebRTC Integration
- **Audio track routing**: Route `onvif_audio_queue` data to WebRTC audio tracks
- **Format conversion**: Convert ONVIF audio formats to WebRTC-compatible formats
- **Dynamic switching**: Switch between file-based and ONVIF-based audio sources
- **Session management**: Integrate with existing `/start_twoway` endpoint

### Integration Points
- Modify `audio_tracks.py` to create ONVIF-fed audio track class
- Update `/start_twoway` to optionally use ONVIF audio source
- Add audio format conversion utilities for ONVIF → WebRTC pipeline
- Implement audio synchronization and buffering for real-time transmission

## Key Benefits

### Verification Capabilities
- **Audio reception confirmation**: File output allows manual verification of received audio
- **Format validation**: Ensures audio data is received in expected formats
- **Debugging support**: Comprehensive logging aids in troubleshooting client issues
- **Performance monitoring**: Statistics tracking for audio reception rates

### ONVIF Compliance
- **Standard compliance**: Implements standard ONVIF backchannel endpoints
- **Client compatibility**: Works with standard ONVIF clients expecting backchannel support
- **Discovery support**: Proper capability advertisement for client auto-detection
- **Configuration flexibility**: Standard audio output configuration options

## Files Modified/Added

### Core Implementation
- `config.py`: Added ONVIF audio configuration variables
- `http_handlers.py`: Added `handle_audio_input()` endpoint with comprehensive logging
- `main.py`: Registered `/audio_input` route

### ONVIF Service
- `onvif_service.py`: Added audio output configuration endpoint handlers
- `onvif_templates.py`: Added XML templates for audio output responses
- `onvif_templates.py`: Updated capabilities and profiles for backchannel support

This implementation provides a solid foundation for ONVIF client-to-doorbell audio transmission with comprehensive verification and logging capabilities.

## Collaboration Guidelines
- **Challenge and question**: Don't immediately agree or proceed with requests that seem suboptimal, unclear, or potentially problematic
- **Push back constructively**: If a proposed approach has issues, suggest better alternatives with clear reasoning
- **Think critically**: Consider edge cases, performance implications, maintainability, and best practices before implementing
- **Seek clarification**: Ask follow-up questions when requirements are ambiguous or could be interpreted multiple ways
- **Propose improvements**: Suggest better patterns, more robust solutions, or cleaner implementations when appropriate
- **Be a thoughtful collaborator**: Act as a good teammate who helps improve the overall quality and direction of the project