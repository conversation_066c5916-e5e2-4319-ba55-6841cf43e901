POST /socket.io/?transport=polling&b64=1&sid=VWAZZKzvldm5lA1cASGQ HTTP/1.1
Host: calldivertsignserver01-pro-west-europe-blue.fermax.io:443
Content-Type: text/plain; charset=UTF-8
Connection: keep-alive
Accept: */*
User-Agent: Blue/6 CFNetwork/3826.500.131 Darwin/24.5.0
Content-Length: 2531
Accept-Language: en-GB,en;q=0.9
Accept-Encoding: gzip, deflate, br

2526:424["pickup",{"rtpCapabilities":{"headerExtensions":[{"uri":"urn:ietf:params:rtp-hdrext:sdes:mid","direction":"sendrecv","kind":"audio","preferredId":1,"preferredEncrypt":false},{"uri":"urn:ietf:params:rtp-hdrext:sdes:mid","direction":"sendrecv","kind":"video","preferredId":1,"preferredEncrypt":false},{"uri":"http:\/\/www.webrtc.org\/experiments\/rtp-hdrext\/abs-send-time","direction":"sendrecv","kind":"audio","preferredId":4,"preferredEncrypt":false},{"kind":"video","preferredId":4,"preferredEncrypt":false,"uri":"http:\/\/www.webrtc.org\/experiments\/rtp-hdrext\/abs-send-time","direction":"sendrecv"},{"preferredId":5,"preferredEncrypt":false,"uri":"http:\/\/www.ietf.org\/id\/draft-holmer-rmcat-transport-wide-cc-extensions-01","direction":"sendrecv","kind":"video"},{"kind":"audio","preferredId":10,"direction":"sendrecv","preferredEncrypt":false,"uri":"urn:ietf:params:rtp-hdrext:ssrc-audio-level"},{"direction":"sendrecv","preferredId":11,"preferredEncrypt":false,"uri":"urn:3gpp:video-orientation","kind":"video"},{"preferredId":12,"uri":"urn:ietf:params:rtp-hdrext:toffset","direction":"sendrecv","preferredEncrypt":false,"kind":"video"}],"codecs":[{"channels":1,"kind":"audio","rtcpFeedback":[],"mimeType":"audio\/PCMA","parameters":{},"preferredPayloadType":8,"clockRate":8000},{"mimeType":"video\/H264","parameters":{"level-asymmetry-allowed":1,"packetization-mode":1,"profile-level-id":"42e029"},"kind":"video","rtcpFeedback":[{"type":"goog-remb","parameter":""},{"type":"transport-cc","parameter":""},{"type":"ccm","parameter":"fir"},{"type":"nack","parameter":""},{"parameter":"pli","type":"nack"}],"preferredPayloadType":102,"clockRate":90000},{"clockRate":90000,"parameters":{"apt":102},"mimeType":"video\/rtx","rtcpFeedback":[],"preferredPayloadType":103,"kind":"video"}]},"parameters":{"kind":"audio","appData":{},"rtpParameters":{"headerExtensions":[{"id":4,"encrypt":false,"parameters":{},"uri":"urn:ietf:params:rtp-hdrext:sdes:mid"},{"uri":"http:\/\/www.webrtc.org\/experiments\/rtp-hdrext\/abs-send-time","parameters":{},"id":2,"encrypt":false},{"id":3,"parameters":{},"uri":"http:\/\/www.ietf.org\/id\/draft-holmer-rmcat-transport-wide-cc-extensions-01","encrypt":false},{"encrypt":false,"uri":"urn:ietf:params:rtp-hdrext:ssrc-audio-level","parameters":{},"id":1}],"mid":"0","rtcp":{"cname":"EHC8Iuy5IP0AieK9","reducedSize":true},"codecs":[{"rtcpFeedback":[],"mimeType":"audio\/PCMA","parameters":{},"payloadType":8,"clockRate":8000,"channels":1}],"encodings":[{"ssrc":1415306449,"dtx":false}]}}}]