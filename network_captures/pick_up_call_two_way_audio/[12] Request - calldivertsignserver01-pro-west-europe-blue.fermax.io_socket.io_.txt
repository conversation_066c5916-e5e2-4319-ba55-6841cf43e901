POST /socket.io/?transport=polling&b64=1&sid=54p7bcxeysp14YzDEopU HTTP/1.1
Host: calldivertsignserver01-pro-west-europe-blue.fermax.io:443
Content-Type: text/plain; charset=UTF-8
Connection: keep-alive
Accept: */*
User-Agent: Blue/6 CFNetwork/3826.500.131 Darwin/24.5.0
Content-Length: 2530
Accept-Language: en-GB,en;q=0.9
Accept-Encoding: gzip, deflate, br

2525:424["pickup",{"rtpCapabilities":{"codecs":[{"kind":"audio","preferredPayloadType":8,"clockRate":8000,"rtcpFeedback":[],"mimeType":"audio\/PCMA","channels":1,"parameters":{}},{"preferredPayloadType":102,"rtcpFeedback":[{"parameter":"","type":"goog-remb"},{"type":"transport-cc","parameter":""},{"parameter":"fir","type":"ccm"},{"type":"nack","parameter":""},{"type":"nack","parameter":"pli"}],"kind":"video","mimeType":"video\/H264","parameters":{"profile-level-id":"42e029","level-asymmetry-allowed":1,"packetization-mode":1},"clockRate":90000},{"preferredPayloadType":103,"kind":"video","mimeType":"video\/rtx","rtcpFeedback":[],"parameters":{"apt":102},"clockRate":90000}],"headerExtensions":[{"uri":"urn:ietf:params:rtp-hdrext:sdes:mid","kind":"audio","preferredId":1,"preferredEncrypt":false,"direction":"sendrecv"},{"direction":"sendrecv","uri":"urn:ietf:params:rtp-hdrext:sdes:mid","kind":"video","preferredId":1,"preferredEncrypt":false},{"preferredId":4,"uri":"http:\/\/www.webrtc.org\/experiments\/rtp-hdrext\/abs-send-time","preferredEncrypt":false,"kind":"audio","direction":"sendrecv"},{"preferredEncrypt":false,"direction":"sendrecv","preferredId":4,"kind":"video","uri":"http:\/\/www.webrtc.org\/experiments\/rtp-hdrext\/abs-send-time"},{"direction":"sendrecv","uri":"http:\/\/www.ietf.org\/id\/draft-holmer-rmcat-transport-wide-cc-extensions-01","preferredEncrypt":false,"kind":"video","preferredId":5},{"direction":"sendrecv","preferredEncrypt":false,"uri":"urn:ietf:params:rtp-hdrext:ssrc-audio-level","preferredId":10,"kind":"audio"},{"uri":"urn:3gpp:video-orientation","preferredId":11,"direction":"sendrecv","kind":"video","preferredEncrypt":false},{"direction":"sendrecv","preferredId":12,"preferredEncrypt":false,"uri":"urn:ietf:params:rtp-hdrext:toffset","kind":"video"}]},"parameters":{"kind":"audio","appData":{},"rtpParameters":{"mid":"0","encodings":[{"ssrc":560592908,"dtx":false}],"codecs":[{"parameters":{},"rtcpFeedback":[],"clockRate":8000,"mimeType":"audio\/PCMA","payloadType":8,"channels":1}],"rtcp":{"reducedSize":true,"cname":"+uWPcc6yz9e2AVko"},"headerExtensions":[{"parameters":{},"id":4,"uri":"urn:ietf:params:rtp-hdrext:sdes:mid","encrypt":false},{"parameters":{},"id":2,"uri":"http:\/\/www.webrtc.org\/experiments\/rtp-hdrext\/abs-send-time","encrypt":false},{"id":3,"uri":"http:\/\/www.ietf.org\/id\/draft-holmer-rmcat-transport-wide-cc-extensions-01","encrypt":false,"parameters":{}},{"parameters":{},"uri":"urn:ietf:params:rtp-hdrext:ssrc-audio-level","encrypt":false,"id":1}]}}}]