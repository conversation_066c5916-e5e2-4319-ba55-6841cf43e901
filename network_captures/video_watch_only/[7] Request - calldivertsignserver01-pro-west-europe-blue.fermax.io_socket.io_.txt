POST /socket.io/?transport=polling&b64=1&sid=PbOX0-or1T2U7BmrEoIa HTTP/1.1
Host: calldivertsignserver01-pro-west-europe-blue.fermax.io:443
Content-Type: text/plain; charset=UTF-8
Connection: keep-alive
Accept: */*
User-Agent: Blue/6 CFNetwork/3826.500.131 Darwin/24.5.0
Content-Length: 1918
Accept-Language: en-GB,en;q=0.9
Accept-Encoding: gzip, deflate, br

1913:421["transport_consume",{"transportId":"81b907a4-b23a-416a-92ed-3e98ee9d1302","producerId":"17ded9d2-3318-4e40-bf87-e09f06a3d59d","rtpCapabilities":{"headerExtensions":[{"direction":"sendrecv","uri":"urn:ietf:params:rtp-hdrext:sdes:mid","preferredId":1,"preferredEncrypt":false,"kind":"audio"},{"kind":"video","direction":"sendrecv","preferredEncrypt":false,"uri":"urn:ietf:params:rtp-hdrext:sdes:mid","preferredId":1},{"preferredId":4,"preferredEncrypt":false,"direction":"sendrecv","uri":"http:\/\/www.webrtc.org\/experiments\/rtp-hdrext\/abs-send-time","kind":"audio"},{"uri":"http:\/\/www.webrtc.org\/experiments\/rtp-hdrext\/abs-send-time","preferredEncrypt":false,"preferredId":4,"kind":"video","direction":"sendrecv"},{"preferredId":5,"uri":"http:\/\/www.ietf.org\/id\/draft-holmer-rmcat-transport-wide-cc-extensions-01","direction":"sendrecv","kind":"video","preferredEncrypt":false},{"uri":"urn:ietf:params:rtp-hdrext:ssrc-audio-level","preferredId":10,"preferredEncrypt":false,"direction":"sendrecv","kind":"audio"},{"uri":"urn:3gpp:video-orientation","preferredId":11,"preferredEncrypt":false,"kind":"video","direction":"sendrecv"},{"kind":"video","uri":"urn:ietf:params:rtp-hdrext:toffset","preferredId":12,"preferredEncrypt":false,"direction":"sendrecv"}],"codecs":[{"channels":1,"parameters":{},"kind":"audio","preferredPayloadType":8,"rtcpFeedback":[],"clockRate":8000,"mimeType":"audio\/PCMA"},{"preferredPayloadType":102,"mimeType":"video\/H264","clockRate":90000,"parameters":{"level-asymmetry-allowed":1,"packetization-mode":1,"profile-level-id":"42e029"},"kind":"video","rtcpFeedback":[{"parameter":"","type":"goog-remb"},{"type":"transport-cc","parameter":""},{"type":"ccm","parameter":"fir"},{"parameter":"","type":"nack"},{"parameter":"pli","type":"nack"}]},{"rtcpFeedback":[],"parameters":{"apt":102},"kind":"video","clockRate":90000,"mimeType":"video\/rtx","preferredPayloadType":103}]}}]