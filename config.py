import asyncio
from uuid import uuid4

# --- Configuration ---
ROOM_ID = None
FERMAX_OAUTH_TOKEN = None
APP_TOKEN = None
SERVER_URL = None

# --- Persistent Stream Configuration ---
MEDIAMTX_URL = "rtsp://127.0.0.1:8554/doorbell"
STILL_IMAGE_PATH = "outside_still.jpg"
VIDEO_WIDTH = 854
VIDEO_HEIGHT = 480
DUMMY_FPS = 24
ONVIF_PORT = 8080
RTSP_PORT = 8554

# --- Globals for State Management ---
current_stream_task = None
ffmpeg_process = None
mediamtx_process = None
live_frame_queue = asyncio.Queue(maxsize=10)  # Reduced from 60 to minimize video delay
live_audio_queue = asyncio.Queue(maxsize=120)  # Audio needs more buffering due to higher frequency
is_live = asyncio.Event()
is_audio_live = asyncio.Event()
g_event_queue = asyncio.Queue()
g_event_available = asyncio.Event()
audio_fifo_path = None

# Snapshot state management for doorbell press
frame_saved = asyncio.Event()  # Flag to track when first frame is saved
doorbell_snapshot_until = None  # Timestamp until when to use doorbell snapshot

# Snapshot update functionality  
snapshot_saved = asyncio.Event()  # Flag to track when snapshot update is complete
custom_save_path = None  # Path to save custom snapshot (None = use default behavior)

# ONVIF audio input configuration
onvif_audio_queue = asyncio.Queue(maxsize=200)  # Buffer for incoming ONVIF audio data
onvif_audio_logging_enabled = True  # Enable detailed audio logging
onvif_audio_save_to_file = True  # Save received audio to files for verification
onvif_audio_file_rotation_count = 10  # Keep last N audio files, delete older ones

# Audio source configuration
# Options: "file", "onvif", "tone", "speech_like", "silence"
AUDIO_SOURCE = "onvif"  # Default to file-based audio, change to "onvif" for real-time ONVIF audio

# Memory protection settings
MEMORY_LIMIT_MB = 2048  # Kill process if memory exceeds this (2GB - allows debugging while preventing OOM)

# --- ONVIF Device Information ---
ONVIF_DEVICE_UUID = "cb16f597-fdd6-4346-a53d-36316af8b787"
ONVIF_SERIAL_NUMBER = ONVIF_DEVICE_UUID.split('-')[-1]

RTP_CAPABILITIES = { "codecs": [ { "kind": "audio", "mimeType": "audio/PCMA", "clockRate": 8000, "preferredPayloadType": 8, "channels": 1, "rtcpFeedback": [], "parameters": {}, }, { "kind": "video", "mimeType": "video/H264", "clockRate": 90000, "preferredPayloadType": 102, "rtcpFeedback": [ {"type": "goog-remb", "parameter": ""}, {"type": "transport-cc", "parameter": ""}, {"type": "ccm", "parameter": "fir"}, {"type": "nack", "parameter": ""}, {"type": "nack", "parameter": "pli"}, ], "parameters": { "packetization-mode": 1, "profile-level-id": "42e029", "level-asymmetry-allowed": 1, }, }, { "kind": "video", "mimeType": "video/rtx", "clockRate": 90000, "preferredPayloadType": 103, "rtcpFeedback": [], "parameters": {"apt": 102}, }, ], "headerExtensions": [ {"kind": "audio", "uri": "urn:ietf:params:rtp-hdrext:sdes:mid", "preferredId": 1, "direction": "sendrecv", "preferredEncrypt": False}, {"kind": "video", "uri": "urn:ietf:params:rtp-hdrext:sdes:mid", "preferredId": 1, "direction": "sendrecv", "preferredEncrypt": False}, {"kind": "audio", "uri": "http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time", "preferredId": 4, "direction": "sendrecv", "preferredEncrypt": False}, {"kind": "video", "uri": "http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time", "preferredId": 4, "direction": "sendrecv", "preferredEncrypt": False}, {"kind": "video", "uri": "http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01", "preferredId": 5, "direction": "sendrecv", "preferredEncrypt": False}, {"kind": "audio", "uri": "urn:ietf:params:rtp-hdrext:ssrc-audio-level", "preferredId": 10, "direction": "sendrecv", "preferredEncrypt": False}, {"kind": "video", "uri": "urn:3gpp:video-orientation", "preferredId": 11, "direction": "sendrecv", "preferredEncrypt": False}, {"kind": "video", "uri": "urn:ietf:params:rtp-hdrext:toffset", "preferredId": 12, "direction": "sendrecv", "preferredEncrypt": False}, ], }