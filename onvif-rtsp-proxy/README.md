# ONVIF RTSP Proxy

An ONVIF Profile T compliant RTSP server that provides backchannel audio support for real-time audio transmission from ONVIF clients to the Fermax doorbell system.

## Features

- ✅ **ONVIF Profile T Compliance**: Implements ONVIF Streaming Specification v23.06 section 5.3
- ✅ **RTSP Backchannel Support**: Handles `www.onvif.org/ver20/backchannel` feature tag
- ✅ **G.711 Audio Processing**: Supports PCMA (A-law) audio format at 8kHz
- ✅ **Real-time Audio Forwarding**: Streams audio directly to Python application
- ✅ **Scrypted Compatible**: Works with ONVIF clients like Scrypted NVR

## Architecture

```
ONVIF Client (Scrypted) → Go RTSP Proxy → Python App → WebRTC → Fermax Doorbell
                         (Port 8555)    (Port 8080)
```

## Quick Start

### 1. Install Dependencies

```bash
cd onvif-rtsp-proxy
go mod tidy
```

### 2. Build and Run

```bash
go build -o onvif-rtsp-proxy main.go
./onvif-rtsp-proxy
```

### 3. Configure ONVIF Client

Point your ONVIF client (e.g., Scrypted) to:
```
rtsp://YOUR_SERVER_IP:8555/doorbell
```

## Configuration

Default configuration:
- **RTSP Port**: 8555
- **Python Endpoint**: http://localhost:8080/audio_input
- **Video Source**: rtsp://localhost:8554/doorbell (MediaMTX)

## ONVIF Protocol Implementation

### Backchannel Requirements

Per ONVIF Streaming Specification section 5.3, the proxy implements:

1. **Feature Tag Support**: Responds to `www.onvif.org/ver20/backchannel` in RTSP Require headers
2. **SDP Generation**: Creates proper Session Description Protocol with backchannel media
3. **Audio Direction**: Sets `a=sendonly` for client-to-server audio transmission
4. **G.711 Format**: Supports standard PCMA payload type 8

### RTSP Flow

1. **DESCRIBE**: Client requests stream description with backchannel requirement
2. **SETUP**: Establishes transport parameters for video and audio tracks
3. **PLAY**: Starts streaming; audio packets received via backchannel
4. **Audio Processing**: G.711 packets extracted and forwarded to Python

## Integration with Python App

The proxy forwards received audio to the Python application's existing `/audio_input` endpoint:

```http
POST /audio_input HTTP/1.1
Content-Type: audio/pcm
Content-Length: 160

[G.711 PCMA audio data]
```

This integrates seamlessly with the existing ONVIF backchannel infrastructure documented in `CLAUDE.md`.

## Testing

### Test with FFmpeg

```bash
# Send test audio to the backchannel
ffmpeg -re -f lavfi -i "sine=frequency=1000:duration=10" \
       -acodec pcm_alaw -ar 8000 -ac 1 \
       -f rtp rtp://localhost:8555?rtcpport=8556
```

### Monitor Logs

The proxy provides detailed logging:
- 🔗 Connection events
- 📋 RTSP message handling  
- 🎤 Backchannel audio reception
- ✅ Audio forwarding status

## Production Deployment

For production use:

1. **Security**: Add authentication to RTSP endpoints
2. **Video Forwarding**: Replace dummy video with actual MediaMTX stream proxy
3. **Error Handling**: Implement retry logic for Python connectivity
4. **Monitoring**: Add metrics and health checks

## Technical Notes

- Uses `gortsplib` for robust RTSP implementation
- Compliant with ONVIF Streaming Specification v23.06
- Designed for integration with existing Fermax-ONVIF bridge
- Supports real-time audio streaming without file dependencies

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure port 8555 is available
2. **Python Connectivity**: Verify Python app is running on port 8080
3. **Audio Format**: Confirm client sends G.711 PCMA format
4. **ONVIF Support**: Check client supports `www.onvif.org/ver20/backchannel`

### Debug Logs

Enable verbose logging to troubleshoot RTSP negotiations and audio forwarding.