#!/bin/bash

# ONVIF RTSP Proxy Startup Script
# Builds and runs the ONVIF-compliant RTSP server with backchannel support

set -e

PROXY_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BINARY_NAME="onvif-rtsp-proxy"

echo "🌟 ONVIF RTSP Proxy Startup"
echo "📁 Working directory: $PROXY_DIR"

cd "$PROXY_DIR"

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.21+ to run the ONVIF RTSP Proxy."
    exit 1
fi

# Install dependencies
echo "📦 Installing Go dependencies..."
go mod tidy

# Build the proxy
echo "🔨 Building ONVIF RTSP Proxy..."
go build -o "$BINARY_NAME" main.go

# Check if binary was created
if [ ! -f "$BINARY_NAME" ]; then
    echo "❌ Failed to build ONVIF RTSP Proxy binary"
    exit 1
fi

echo "✅ Build successful"

# Check if Python app is running
echo "🐍 Checking Python application connectivity..."
if curl -s -f http://localhost:8080/audio_input > /dev/null 2>&1; then
    echo "✅ Python application is accessible on port 8080"
else
    echo "⚠️  Warning: Python application may not be running on port 8080"
    echo "   Make sure to start the main Python application first"
fi

# Check if MediaMTX is running
echo "📺 Checking MediaMTX video source..."
if curl -s -f rtsp://localhost:8554/doorbell > /dev/null 2>&1; then
    echo "✅ MediaMTX video source is accessible"
else
    echo "⚠️  Warning: MediaMTX may not be running on port 8554"
    echo "   The proxy will use dummy video until MediaMTX is available"
fi

# Start the proxy
echo ""
echo "🚀 Starting ONVIF RTSP Proxy..."
echo "📡 RTSP server will be available at: rtsp://localhost:8555/doorbell"
echo "🎤 Audio backchannel support: ENABLED (ONVIF Profile T)"
echo "🔗 Audio forwarding to: http://localhost:8080/audio_input"
echo ""
echo "Press Ctrl+C to stop the proxy"
echo ""

# Run the proxy
./"$BINARY_NAME"